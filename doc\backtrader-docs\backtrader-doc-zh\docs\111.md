# 开发递归指示器（带种子）

> 原文： [https://www.backtrader.com/blog/posts/2018-01-27-recursive-indicators/recursive-indicator/](https://www.backtrader.com/blog/posts/2018-01-27-recursive-indicators/recursive-indicator/)

*反向交易者*的初始目标之一是：

*   能够快速原型化指标以测试新想法

它不一定是一个完美的指标，但能够快速、轻松地开发它们确实有帮助。为了确认设计是正确的，*反向交易者*标准库中的第一个指标是*指数移动平均数*（又称*均线*），根据定义是：**递归**。

笔记

琐事：正如你所想象的，1<sup>st</sup>指标是一个*简单的平均值*

既然如何开发递归指标的问题已经发布在[backtrader 社区](https://community.backtrader.com/topic/833/indicator-values-before-period-kicks-in)中，让我们快速开发一个`ExponentialMovingAverage`指标。

递归指示符，如

*   它使用以前的值来计算当前值

你可以在[维基百科——指数移动平均](https://en.wikipedia.org/wiki/Moving_average#Exponential_moving_average)中看到数学示例

如果您有足够的勇气阅读所有内容，您将看到周期用于计算*指数平滑*。我们会用的。

为了解决计算第一个值的难题，*行业*决定使用之前`period`值的简单平均值。

作为杠杆，我们将使用`bt.indicators.PeriodN`，其中：

*   已经定义了一个`period`参数

*   通知框架最终用户使用的实际`period`

定义见：[单据-指标参考](https://www.backtrader.com/docu/indautoref.html)

然后让我们开发我们的`EMA`

```py
import backtrader as bt

class EMA(bt.indicators.PeriodN):
    params = {'period': 30}  # even if defined, we can redefine the default value
    lines = ('ema',)  # our output line

    def __init__(self):
        self.alpha = 2.0 / (1.0 + self.p.period)  # period -> exp smoothing factor

    def nextstart(self):  # calculate here the seed value
        self.lines.ema[0] = sum(self.data.get(size=self.p.period)) / self.p.period

    def next(self):
        ema1 = self.lines.ema[-1]  # previous EMA value
        self.lines.ema[0] = ema1 * (1.0 - self.alpha) + self.data[0] * self.alpha 
```

做起来比说起来容易。键是在`nextstart`中提供种子值，其中

*   当达到指示器的最小预热周期时，将调用一次。

    与`next`相反，随后将为每个传递到系统中的新数据值调用`next`

`nextstart`的默认实现只是将作业委托给`next`，对于大多数指标（例如*简单移动平均线*）来说，这是正确的做法。但在这种情况下，覆盖和提供种子值是关键。

## 沿着数据绘制它

作为移动平均线，如果指标与计算平均值的数据在同一轴上绘制，那就更好了。因为我们从`PeriodN`继承了绘图的默认值（见文档）：

```py
subplot=True 
```

这当然意味着将为我们的指标创建一个`subplot`（图表上的另一个轴）。这很容易被推翻。

```py
import backtrader as bt

class EMA(bt.indicators.PeriodN):
    plot = dict(subplot=False) 
```

完成了。如果要控制更多打印选项，请选中[文档-打印](https://www.backtrader.com/docu/plotting/plotting.html)

祝你好运