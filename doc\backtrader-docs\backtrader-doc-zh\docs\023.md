# 例外情况

> 原文： [https://www.backtrader.com/docu/exceptions/](https://www.backtrader.com/docu/exceptions/)

设计目标之一是尽早退出，让用户完全透明地了解错误发生的情况。其目标是强迫自己拥有会在异常时中断的代码，并强制重新访问受影响的部分。

但时机已经成熟，一些例外情况可能会慢慢添加到平台中。

## 等级制度

所有异常的基类都是`BacktraderError`（是`Exception`的直接子类）

## 地方

1.  在模块`errors`内，可访问该模块，例如：

    ```py
    import backtrader as bt

    class Strategy(bt.Strategy):

        def __init__(self):
            if something_goes_wrong():
                raise bt.errors.StrategySkipError 
    ```

2.  直接从`backtrader`开始，如：

    ```py
    import backtrader as bt

    class Strategy(bt.Strategy):

        def __init__(self):
            if something_goes_wrong():
                raise bt.StrategySkipError 
    ```

## 例外情况

### `StrategySkipError`

请求平台跳过此策略进行回溯测试。在实例的初始化（`__init__`阶段引发