# 谁在用它

> 原文： [https://www.backtrader.com/home/<USER>/who-is-using-it/](https://www.backtrader.com/home/<USER>/who-is-using-it/)

有关用法的参考信息，请参见本章后面的章节。

## 旧网站参考

原网站声明*反向交易者*用于（至少）：

*   2 家欧洲斯托克 50 银行

*   6 家定量贸易公司

这是很久以前的事了，作者知道的还有很多，包括*“能源贸易”*等行业的其他类型的贸易公司。

## 为什么没有名字？

1.  我从来没问过

2.  他们从来没有问过

## 那么，你能支持这些主张吗？

已经在[Reddit 中问过（并回答过）【问题】这艘潜艇上的 Backtrader 有多受欢迎？](https://www.reddit.com/r/algotrading/comments/9k51kt/question_how_popular_is_backtrader_on_this_sub/)

 *让我们引用这条线索的答案。

引用

没有，没有清单。这实际上已经过时了：银行的数量仍然保持在`2`（可能还有更多，但我不知道），但有 6 家以上的公司将其用于内部目的，包括在能源市场工作的公司，因为`compensation`功能允许买卖不同的资产，相互补偿（这可能在`zipline`中不可用），允许使用现货和期货价格进行建模（这是能源市场的特殊性，以避免实际货物交付给您）

这里的问题是必须定义**用法**。例如，我可以引用其中一家银行的一位人士告诉我的话：*“我们使用 backtrader 来快速原型化我们的想法，并对其进行回溯测试。如果这些想法被证明是我们所期望的，并且经过进一步完善，它们将用 Java 重写，并放入我们的生产系统”*

这实际上是一个 Quant 公司（我亲自访问过）使用的方案：在*backtrader*中进行原型制作，在*Java*中进行生产。

正如你可能想象的那样，我没有跟踪那些使用*backtrader*的人的生活，因此也可能是一些银行和公司决定不再使用*backtrader*。

我也确实猜测，一些银行和定量公司使用`zipline`遵循相同的方案。*