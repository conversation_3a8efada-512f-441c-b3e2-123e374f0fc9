# 数据源

> 原文： [https://www.backtrader.com/docu/datafeed/](https://www.backtrader.com/docu/datafeed/)

`backtrader`附带了一套数据源解析器（在编写所有基于 CSV 的文档时），允许您从不同来源加载数据。

*   雅虎（在线或已保存到文件）

*   VisualChart（见[www.VisualChart.com](http://www.visualchart.com)

*   Backtrader CSV（自己制作的测试格式）

*   通用 CSV 支持

从《快速入门指南》中可以清楚地看到，您向`Cerebro`实例添加了数据提要。数据源稍后将提供给以下不同的策略：

*   数组 self.datas（插入顺序）

*   阵列对象的别名：

    *   self.data 和 self.data0 指向第一个元素

    *   self.dataX 指向数组中索引为 X 的元素

有关插入工作原理的快速提醒：

```py
import backtrader as bt
import backtrader.feeds as btfeeds

data = btfeeds.YahooFinanceCSVData(dataname='wheremydatacsvis.csv')

cerebro = bt.Cerebro()

cerebro.adddata(data)  # a 'name' parameter can be passed for plotting purposes 
```

## 数据提供公共参数

这个数据源可以直接从 Yahoo 下载数据并输入系统。

参数：

*   必须提供`dataname`（默认为无）

    含义因数据馈送类型（文件位置、代码等）而异

*   `name`（默认值：“”）

    用于绘图时的装饰目的。如果未指定，则可从`dataname`派生（例如：文件路径的最后一部分）

*   `fromdate`（默认值：mindate）

    Python datetime 对象，指示在此之前的任何 datetime 都应被忽略

*   `todate`（默认为 maxdate）

    Python datetime 对象，指示在此之后的任何 datetime 都应被忽略

*   `timeframe`（默认值：TimeFrame.Days）

    电位值：`Ticks`、`Seconds`、`Minutes`、`Days`、`Weeks`、`Months`和`Years`

*   `compression`（默认值：1）

    每根钢筋的实际钢筋数量。提供有用信息的仅在数据重采样/重放时有效。

*   `sessionstart`（默认为无）

    指示数据的会话开始时间。可由类用于重采样等目的

*   `sessionend`（默认为无）

    指示数据的会话结束时间。可由类用于重采样等目的

## CSV 数据提供公共参数

参数（除常用参数外）：

*   `headers`（默认为 True）

    指示传递的数据是否具有初始标题行

*   `separator`（默认值：“，”）

    标记每个 CSV 行时要考虑的分隔符

## 通用 CSVDATA

这个类公开了一个通用接口，允许解析大部分 CSV 文件格式。

根据参数定义的顺序和字段显示来解析 CSV 文件

具体参数（或具体含义）：

*   `dataname`

    要分析的文件名或类似文件的对象

*   包含日期（或日期时间）字段的`datetime`（默认值：0）列

*   `time`（默认值：-1）如果与日期时间字段分开，则包含时间字段的列（-1 表示不存在）

*   `open`（默认值：1）、`high`（默认值：2）、`low`（默认值：3）、`close`（默认值：4）、`volume`（默认值：5）、`openinterest`（默认值：6）

    包含相应字段的列的索引

    如果传递负值（例如：-1），则表示该字段不存在于 CSV 数据中

*   `nullvalue`（默认值：浮点（'NaN'））

    如果缺少应存在的值（CSV 字段为空），将使用的值

*   `dtformat`（默认值：%Y-%m-%d%H:%m:%S）

    用于分析 datetime CSV 字段的格式

*   `tmformat`（默认值：%H:%M:%S）

    用于在“存在”时解析时间 CSV 字段的格式（“时间”CSV 字段的默认值不存在）

包含以下要求的示例用法：

*   将投入限制在 2000 年

*   HLOC 订单而非 OHLC

*   缺少要替换为零（0.0）的值

*   提供每日条形图，日期时间仅为 YYYY-MM-DD 格式的日期

*   不存在`openinterest`列

守则：

```py
import datetime
import backtrader as bt
import backtrader.feeds as btfeeds

...
...

data = btfeeds.GenericCSVData(
    dataname='mydata.csv',

    fromdate=datetime.datetime(2000, 1, 1),
    todate=datetime.datetime(2000, 12, 31),

    nullvalue=0.0,

    dtformat=('%Y-%m-%d'),

    datetime=0,
    high=1,
    low=2,
    open=3,
    close=4,
    volume=5,
    openinterest=-1
)

... 
```

稍作修改的要求：

*   将投入限制在 2000 年

*   HLOC 订单而非 OHLC

*   缺少要替换为零（0.0）的值

*   提供日内条形图，带有单独的日期和时间列

    *   日期的格式为 YYYY-MM-DD
    *   时间的格式为 HH.MM.SS（而不是通常的 HH:MM:SS）
*   不存在`openinterest`列

守则：

```py
import datetime
import backtrader as bt
import backtrader.feeds as btfeed

...
...

data = btfeeds.GenericCSVData(
    dataname='mydata.csv',

    fromdate=datetime.datetime(2000, 1, 1),
    todate=datetime.datetime(2000, 12, 31),

    nullvalue=0.0,

    dtformat=('%Y-%m-%d'),
    tmformat=('%H.%M.%S'),

    datetime=0,
    time=1,
    high=2,
    low=3,
    open=4,
    close=5,
    volume=6,
    openinterest=-1
) 
```

这也可以通过子类化使*永久*：

```py
import datetime
import backtrader.feeds as btfeed

class MyHLOC(btfreeds.GenericCSVData):

  params = (
    ('fromdate', datetime.datetime(2000, 1, 1)),
    ('todate', datetime.datetime(2000, 12, 31)),
    ('nullvalue', 0.0),
    ('dtformat', ('%Y-%m-%d')),
    ('tmformat', ('%H.%M.%S')),

    ('datetime', 0),
    ('time', 1),
    ('high', 2),
    ('low', 3),
    ('open', 4),
    ('close', 5),
    ('volume', 6),
    ('openinterest', -1)
) 
```

现在只需提供`dataname`即可重用这个新类：

```py
data = btfeeds.MyHLOC(dataname='mydata.csv') 
```