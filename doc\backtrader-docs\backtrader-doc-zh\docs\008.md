# 博客-文章

> 原文： [https://www.backtrader.com/home/<USER>/blogs/](https://www.backtrader.com/home/<USER>/blogs/)

显示 backtrader 正在运行的博客列表。

### 阿杜尼克

*   [这就是我在 Backtrader（Python）](https://aadhunik.ml/this-is-how-i-implemented-super-trend-indicator-in-backtrader-python/)*中实现超级趋势指示器的方式*

 *[https://aadhunik.ml/this-is-how-i-implemented-super-trend-indicator-in-backtrader-python/](https://aadhunik.ml/this-is-how-i-implemented-super-trend-indicator-in-backtrader-python/)

### 精算数据科学

*   [使用 Backtrader](http://actuarialdatascience.com/backtrader_performance_report.html)*创建绩效报告*

 *### α对β

*   [交易波动性对冲权益 https://www.alphaoverbeta.net/trading-volatility-to-hedge-equity/ -](https://www.alphaoverbeta.net/trading-volatility-to-hedge-equity/)

 *### 分析阿尔法

*   [反向交易者：开始反向测试-https://analyzingalpha.com/backtrader-backtesting-trading-strategies](https://analyzingalpha.com/backtrader-backtesting-trading-strategies)

**   [板块动量：解释&回溯测试-https://analyzingalpha.com/sector-momentum](https://analyzingalpha.com/sector-momentum)

    **   [止损：解释&最佳策略-https://analyzingalpha.com/stop-loss-for-stocks](https://analyzingalpha.com/stop-loss-for-stocks)

    **   [2019 年前 21 大 Python 交易工具-https://analyzingalpha.com/python-trading-tools](https://analyzingalpha.com/python-trading-tools)*** 

 ***### 安德烈亚斯·克莱诺——追随潮流

*   [使用 Python](https://www.followingthetrend.com/2018/06/backtesting-with-python/)*进行回溯测试*

 *### 天使名单-贾斯汀·瓦格纳

*   [加密货币机器人，具有编码策略和回溯测试](https://angel.co/projects/729346-cryptocurrency-bot-with-encoded-strategies-and-backtesting?src=user_profile)

 *### 回溯测试新手（几个职位）

*   [反向交易者：数据回放](https://backtest-rookies.com/2017/12/06/backtrader-data-replay/)

 *### CSDN-钱塘夏家子

*   [反向交易者量化平台教程（8）时间框架](https://blog.csdn.net/qtlyx/article/details/72716995)

     ***语言**：*中文** 

 *### DevTo-dennislwm

*   [如何在 4 个 GIF 步骤中对 Backtrader 进行 Dockerize](https://dev.to/dennislwm/how-to-dockerize-backtrader-in-4-gif-steps-4af2)

 *[https://dev.to/dennislwm/how-to-dockerize-backtrader-in-4-gif-steps-4af2](https://dev.to/dennislwm/how-to-dockerize-backtrader-in-4-gif-steps-4af2)

### 埃瑟施特伦普

*   [TOP30 战略 1.0e](https://blog.etherschtroumpf.money/2019/06/04/top30-strategy-1-0/)

 *### Finanzas.com

*   [加密交易算法：分布式系统](http://www.finanzas.com/noticias/mercados/bolsas/20180718/cryptotrading-algoritmico-disenando-sistema-3876840.html)

     ***语言**：*西班牙语** 

 *### Intrinio 博客

*   [与 Intrinio&反向交易者](https://blog.intrinio.com/a-quant-quickstart-with-intrinio-backtrader/)*的定量快速启动*

 *### LinkedIn-Majid AliAkbar

*   [金融和金融数据科学家最佳 Python 库/包](https://www.linkedin.com/pulse/best-python-librariespackages-finance-financial-data-majid-aliakbar/)

 *### LinkedIn-阿里·科卡兹

*   [探索横截面均值回归交易策略](https://www.linkedin.com/pulse/exploring-cross-sectional-mean-reversion-trading-strategy-ali-kokaz-1f)

 *### 中-竹林

*   [算法交易/程序交易](https://medium.com/@chulla/algo-trading-%ED%94%84%EB%A1%9C%EA%B7%B8%EB%9E%A8-%ED%8A%B8%EB%A0%88%EC%9D%B4%EB%94%A9-fd7f88c99834)

 ***语言**：韩语

### 中暗诺林

*   [克林格音量振荡器](https://medium.com/@uachatse/test-for-klinger-volume-oscillator-b234b66a9a84?source=---------2------------------)*测试*

 *### 中等-艾蒂安深色

*   [我对人工智能量化基金和 DIY 基金的看法](https://medium.com/@etiennebr/my-landscape-on-artificial-intelligence-quantitative-funds-and-diy-funds-13e1b4c25706)

 *### 中等-苏米特奥哈

*   [加密货币持有与智能持有](https://medium.com/@ojhasumit93/holding-vs-smart-holding-in-cryptocurrency-c3c9d89c85f)

 *### 面向数据科学的媒介

*   [交易策略：反向测试反向交易者](https://towardsdatascience.com/trading-strategy-back-testing-with-backtrader-6c173f29e37f)

**   [回溯测试您的第一个交易策略](https://towardsdatascience.com/backtesting-your-first-trading-strategy-ad3977f3f2a)

    **   [使用新闻文章情绪分析的算法交易](https://towardsdatascience.com/https-towardsdatascience-com-algorithmic-trading-using-sentiment-analysis-on-news-articles-83db77966704)

    **   [广达关于产生交易想法的建议](https://towardsdatascience.com/quants-advice-on-generating-trading-ideas-580ea62672f3)*** 

 ***### 中等-ttamg

*   [TradingBot 系列 — 交易机器人的架构](https://medium.com/@MattGosden/tradingbot-series-architecture-for-a-trading-bot-ac2352508c82)

 *### 中等-乌古尔阿克约尔

*   [反向交易者机器人](https://medium.com/@UgurAkyol1/walk-forward-worksheet-for-backtrader-81c7571099a1)*向前走工作表*

 *### 中文昌

*   [手写 AI 交易机器人](https://medium.com/@meowent/%E5%8B%95%E6%89%8B%E5%AF%AB%E4%B8%80%E9%9A%BBai%E4%BA%A4%E6%98%93%E6%A9%9F%E5%99%A8%E4%BA%BA-e995500b55f7)

     ***语言**：*中文** 

 *### 中-有谢

*   [IGNIS 空投事件研究](https://medium.com/@xyshell/event-study-on-ignis-airdrop-2f7a93bb24f4)

 *### 我的金融市场

*   [用 Python 对交易策略进行回溯测试](https://myfinancialmarkets.club/2017/11/23/backtest-a-trading-strategy-in-python/)

 *### NTGuardian

*   [反向交易者入门](https://ntguardian.wordpress.com/2017/06/12/getting-started-with-backtrader/)

 *### 利润加达

*   [使用 Backtrader](http://actuarialdatascience.com/backtrader_performance_report.html)*创建绩效报告*

 *### 勾股金融

*   [自动交易比较 Python 平台](https://randlow.github.io/posts/trading/trading-introduction/)

**   [优化交易策略参数](https://randlow.github.io/posts/trading/trading-strat-parameter-opt/)

    **   [为您的交易策略添加指标](https://randlow.github.io/posts/trading/trading-indicators-backtrader/)

    **   [定制交易策略](https://randlow.github.io/posts/trading/trading-custom-backtrader/)

    **   [反向交易者自动交易介绍](https://randlow.github.io/posts/trading/trading-intro-backtrader/)**** 

 ***### 昆坦斯蒂

*   [安装 Python 包](https://blog.quantinsti.com/installing-python-packages/)

 *### Rankia.com

*   [安那利斯塔尼科交易](https://www.rankia.com/blog/blockchain-criptomonedas-bitcoin-ethereum/3968963-trading-criptodivisas-analisis-tecnico)

     ***语言**：*西班牙语** 
**   [在中国进行的交易](https://www.rankia.pt/trading-de-criptomoedas-com-analise-tecnica/)

     ***语言**：*葡萄牙语*** 

 **### 伦布尔博客

*   [均值回归策略](http://renbuar.blogspot.com/2018/05/backtrader-multiple-data-feeds.html)

 *### 科学

*   [与反向交易者](https://ms.sciencewal.com/61369-trading-strategy-back-testing-with-backtrader-6c173f29e37f-94)*进行交易策略反向测试*

     ***语言**：*印尼语** 

 *### 肖特凯利博客

*   [基本算法交易第 2 部分-反向交易](http://seangtkelley.me/blog/2018/08/15/algo-trading-pt2)

 *### 弄脏机

*   [费伯日周策略](https://smudlatrader.wordpress.com/tag/backtrader/)

 *### 特迪·科克

*   [Python](https://teddykoker.com/2019/05/momentum-strategy-from-stocks-on-the-move-in-python/)*中“移动中的股票”的动量策略*

**   [Python 中改进横截面均值回归策略](https://teddykoker.com/2019/05/improving-cross-sectional-mean-reversion-strategy-in-python/)

    **   [在 Python](https://teddykoker.com/2019/04/backtesting-a-cross-sectional-mean-reversion-strategy-in-python/)*中回溯测试横截面均值回归策略*

    **   [使用 Backtrader](https://teddykoker.com/2019/04/backtesting-portfolios-of-leveraged-etfs-in-python-with-backtrader/)*在 Python 中回溯测试杠杆 ETF 的投资组合**** 

 ***### 实验室-斯瓦普尼尔贾里瓦拉

*   [如何将 NSEpy 与 backtrader 一起使用的简单示例](https://swapniljariwala.github.io/2017/11/03/NSEPy-with-backtrader.html)*******************************************