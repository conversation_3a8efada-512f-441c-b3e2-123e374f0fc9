name: AutoCorrect
on: [pull_request]
jobs:
  autocorrect:
    name: Check text autocorrect
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Check
        uses: huacnlee/autocorrect-action@main
        with:
          use_npm: true

      - name: Report ReviewDog
        if: failure()
        uses: huacnlee/autocorrect-action@main
        env:
          REVIEWDOG_GITHUB_API_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          use_npm: true
          reviewdog: true
