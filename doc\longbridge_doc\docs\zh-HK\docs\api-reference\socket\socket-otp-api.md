---
title: 獲取長連接 OTP
id: socket-otp-api
slug: /socket-token-api
sidebar_position: 2
---

# 獲取長連接 OTP(One Time Password)

獲取長連接使用的 `Token`(One time password)，長連接的 `Token` 可以用來連接行情和交易的長連接網關，是一次性的，使用過後就會作廢。

> 最後更新於 2022-04-28

## 請求

| 基本信息    |                  |
| ----------- | ---------------- |
| HTTP URL    | /v1/socket/token |
| HTTP Method | GET              |

### 請求頭

| 名稱          | 類型   | 必須 | 描述                                          |
| ------------- | ------ | ---- | --------------------------------------------- |
| Authorization | string | 是   |                                               |
| Content-Type  | string | 是   | **固定值**："application/json; charset=utf-8" |

### 請求參數

## 響應

### 響應體

| 名稱    | 類型   | 描述                  |
| ------- | ------ | --------------------- |
| code    | int    | 錯誤碼，非 0 表示失敗 |
| msg     | string | 錯誤描述              |
| data    | object |                       |
| ∟otp    | string | 獲取到的 token        |
| ∟limit  | int    | 連接限制總數          |
| ∟online | int    | 當前在線連接數        |

### 響應體示例

```json
{
  "code": 0,
  "message": "",
  "data": {
    "otp": "xxxxxxxx",
    "online": 1,
    "limit": 10
  }
}
```
