{"editor.tabSize": 2, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}, "[css]": {"editor.defaultFormatter": "aeschli.vscode-css-formatter"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[yaml]": {"editor.defaultFormatter": "redhat.vscode-yaml"}, "prettier.requireConfig": true, "prettier.singleQuote": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit"}, "editor.formatOnSave": true, "autocorrect.enable": true, "autocorrect.formatOnSave": true, "[markdown]": {"editor.wordWrap": "on", "editor.quickSuggestions": {"strings": true}}}