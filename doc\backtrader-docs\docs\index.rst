.. backtrader documentation master file, created by
   sphinx-quickstart on Mon Feb 23 13:28:32 2015.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

Welcome to backtrader's documentation!
======================================

Contents:

.. toctree::
   :maxdepth: 3

   introduction
   installation
   quickstart/quickstart
   concepts
   operating
   exceptions
   cerebro
   cerebro/cheat-on-open/cheat-on-open
   strategy
   sizers/sizers
   timers/timers
   order_target/order_target
   signal_strategy/signal_strategy
   broker
   slippage/slippage
   filler
   order
   order-creation-execution/order-creation-execution
   order-creation-execution/oco/oco
   order-creation-execution/trail/stoptrail
   order-creation-execution/bracket/bracket
   order-creation-execution/futurespot/future-vs-spot
   datafeed
   datafeed-develop-csv
   datafeed-develop-general/datafeed-develop-general
   extending-a-datafeed
   pandas-datafeed/pandas-datafeed
   tradingcalendar/tradingcalendar
   data-resampling/data-resampling
   data-multitimeframe/data-multitimeframe
   data-replay/data-replay
   data-rollover/rolling-futures-over
   filters
   induse
   talib/talib
   mixing-timeframes/indicators-mixing-timeframes
   inddev
   observers-and-statistics/observers-and-statistics
   observer-benchmark/benchmarking
   analyzers/analyzers
   analyzers/pyfolio
   analyzers/pyfolio-integration/pyfolio-integration
   writer
   commission-schemes/commission-schemes
   extending-commissions/commission-schemes-extended
   user-defined-commissions/commission-schemes-subclassing
   commission-credit
   position
   trade
   plotting/plotting
   plotting/ranges/plotting-date-ranges
   plotting/sameaxis/plot-sameaxis
   optimization-improvements
   automated-bt-run/automated-bt-run
   memory-savings/memory-savings
   timemgmt
   live/live
   dataautoref
   datayahoo
   indautoref
   talibindautoref
   strategy-reference
   analyzers-reference
   observers-reference
   sizers-reference
   filters-reference

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
