# 填充物

> 原文： [https://www.backtrader.com/docu/filler/](https://www.backtrader.com/docu/filler/)

*backtrader*经纪人模拟在使用交易量执行订单时有一个默认策略：

*   忽略音量

这是基于两个前提：

*   市场交易的流动性足以一次性完全吸收*买入/卖出*订单

*   实卷匹配需要实卷

    一个简单的例子是`Fill or Kill`订单。即使到了*滴答*决议案，并且有足够的交易量进行*填充*，*反向交易者*经纪人也无法知道市场上碰巧有多少额外的参与者来区分这样的订单是否匹配`Fill`部分，或者订单是否应该是`Kill`

但*经纪人*可以接受*交易量填充*，确定在给定时间点有多少交易量需要用于*订单匹配*。

## 签名

*backtrader*生态系统中的*填充物*可以是任何符合以下签名的*可调用*：

```py
callable(order, price, ago) 
```

哪里：

*   `order`是要执行的命令

    此对象允许访问`data`对象，该对象是操作的目标、创建大小/价格、执行价格/大小/剩余大小和其他详细信息

*   `price`执行订单的时间

*   `ago`是*顺序*中`data`的索引，用于查找数量和价格要素

    在几乎所有情况下，这将是`0`（当前时间点），但在涵盖`Close`订单的角落情况下，这可能是`-1`

    例如，要访问条形图卷，请执行以下操作：

    ```py
    barvolume = order.data.volume[ago] 
    ```

可调用函数可以是一个函数，例如支持`__call__`方法的类的实例，如：

```py
class MyFiller(object):
    def __call__(self, order, price, ago):
        pass 
```

## 向代理添加填充程序

最直接的方法是使用`set_filler`：

```py
import backtrader as bt

cerebro = Cerebro()
cerebro.broker.set_filler(bt.broker.fillers.FixedSize()) 
```

第二种选择是完全替换`broker`，尽管这可能仅适用于`BrokerBack`的子类，这些子类重写了部分功能：

```py
import backtrader as bt

cerebro = Cerebro()
filler = bt.broker.fillers.FixedSize()
newbroker = bt.broker.BrokerBack(filler=filler)
cerebro.broker = newbroker 
```

## 样本

*反向交易者*来源包含一个名为`volumefilling`的样本，该样本允许测试一些集成的`fillers`（最初全部）

## 参考

#### 类 backtrader.fillers.FixedSize（）

返回给定订单的执行大小，使用条形图中卷的*百分比*。

此百分比由参数`perc`设置

参数：

*   `size`（默认值：`None`）要执行的最大大小。如果棒的实际体积小于尺寸，则执行时棒的实际体积也是一个限制

    如果此参数的值计算为 False，则将使用条的整个体积来匹配订单

#### 类 backtrader.fillers.FixedBarPerc（）

返回给定订单的执行大小，使用条形图中卷的*百分比*。

此百分比由参数`perc`设置

参数：

*   `perc`（默认值：`100.0`（有效值：`0.0 - 100.0`）

    用于执行订单的卷栏百分比

#### 类 backtrader.fillers.BarPointPerc（）

返回给定订单的执行大小。在*高*-*低*范围内，利用`minmov`进行分区，使容积均匀分布。

根据给定价格的分配量，将使用`perc`百分比

参数：

*   `minmov`（默认为`0.01`）

    最低价格变动。用于划分*高*-*低*区间，在可能的价格中按比例分配成交量

*   `perc`（默认值：`100.0`（有效值：`0.0 - 100.0`）

    分配给订单执行价格用于匹配的卷的百分比