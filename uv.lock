version = 1
revision = 2
requires-python = ">=3.13"

[[package]]
name = "longport"
version = "3.0.8"
source = { registry = "https://pypi.org/simple" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/af/8c/da2b24939c29e692c01679851107e878cecb12789b6d9c9b6cc582de9ec9/longport-3.0.8-cp313-cp313-macosx_10_12_x86_64.macosx_11_0_arm64.macosx_10_12_universal2.whl", hash = "sha256:d689a4bb3019d38ca0be91389852eac3b3c10094fcf97ff53e319ede031aceaa", size = 7584029, upload-time = "2025-07-15T13:20:50.199Z" },
    { url = "https://files.pythonhosted.org/packages/e0/82/a802fee74788be3c5808f4a3cb42cfb654354e17752d2dacbd2f0814f3e9/longport-3.0.8-cp313-cp313-macosx_10_12_x86_64.whl", hash = "sha256:566eb6363963ad830e87d872d91540ce793d855347c1dbbd919b05535cb771e2", size = 3896900, upload-time = "2025-07-15T13:20:52.314Z" },
    { url = "https://files.pythonhosted.org/packages/80/c8/00f0a7e6002db35dad3c5de6e60cfeb472596ff4e767e501240028e60fcc/longport-3.0.8-cp313-cp313-manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:066b99e3eee3c46660f7cc89999d2184bbaf5c2410523a7657cd9d3c13435423", size = 3933733, upload-time = "2025-07-15T13:20:54.024Z" },
    { url = "https://files.pythonhosted.org/packages/5b/30/51d2ebec4535629348a052eac8a5bae8175d6b3fc797df094f5656fdc0a9/longport-3.0.8-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:df0d3dd3c01adf385ae6d9c864230b61d2bcd6b16bda843667745c24057b56d6", size = 4002092, upload-time = "2025-07-15T13:20:55.814Z" },
    { url = "https://files.pythonhosted.org/packages/1d/1f/6d79f4e4926364d233dd0db6cf4018f4bcbc536c086b4309d19c2a8f962e/longport-3.0.8-cp313-cp313-manylinux_2_28_aarch64.whl", hash = "sha256:1743ac8966c4f89d704f295f2c8b84622390d56feaa26a779a014060da67efa1", size = 3863217, upload-time = "2025-07-15T13:20:57.998Z" },
    { url = "https://files.pythonhosted.org/packages/15/be/24523f59923736a66e14d44d33e489617085c44450eff7def56fb0e62452/longport-3.0.8-cp313-none-win32.whl", hash = "sha256:f4f66d136a9fd208411b0cec5749b4ed374376ac3cfc6e6da65b991ee8cbc506", size = 3213404, upload-time = "2025-07-15T13:20:59.701Z" },
    { url = "https://files.pythonhosted.org/packages/01/0d/486c20e6cd997ccbff2e955257d404cf54c790e955bf48f3ab451ce9b7bb/longport-3.0.8-cp313-none-win_amd64.whl", hash = "sha256:8e20499b6602796cc3395edecc9deed057767a9e220e8fc34a8b149dbcd0b270", size = 3723182, upload-time = "2025-07-15T13:21:01.392Z" },
]

[[package]]
name = "narwhals"
version = "1.48.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/fc/cd/7395d6c247e821cba6243e9f7ed202fae3fefef643c96581b5ecab927bad/narwhals-1.48.0.tar.gz", hash = "sha256:7243b456cbdb60edb148731a8f9b203f473a373a249ad66c699362508730e63f", size = 515112, upload-time = "2025-07-21T10:06:08.215Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/75/72/5406044d4c251f3d8f78cec05b74839d0332d34c9e94b59120f3697ecf48/narwhals-1.48.0-py3-none-any.whl", hash = "sha256:2bbddc3adeed0c5b15ead8fe61f1d5e459f00c1d2fa60921e52a0f9bdc06077d", size = 376866, upload-time = "2025-07-21T10:06:06.561Z" },
]

[[package]]
name = "packaging"
version = "25.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/a1/d4/1fc4078c65507b51b96ca8f8c3ba19e6a61c8253c72794544580a7b6c24d/packaging-25.0.tar.gz", hash = "sha256:d443872c98d677bf60f6a1f2f8c1cb748e8fe762d2bf9d3148b5599295b0fc4f", size = 165727, upload-time = "2025-04-19T11:48:59.673Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/20/12/38679034af332785aac8774540895e234f4d07f7545804097de4b666afd8/packaging-25.0-py3-none-any.whl", hash = "sha256:29572ef2b1f17581046b3a2227d5c611fb25ec70ca1ba8554b24b0e69331a484", size = 66469, upload-time = "2025-04-19T11:48:57.875Z" },
]

[[package]]
name = "plotly"
version = "6.2.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "narwhals" },
    { name = "packaging" },
]
sdist = { url = "https://files.pythonhosted.org/packages/6e/5c/0efc297df362b88b74957a230af61cd6929f531f72f48063e8408702ffba/plotly-6.2.0.tar.gz", hash = "sha256:9dfa23c328000f16c928beb68927444c1ab9eae837d1fe648dbcda5360c7953d", size = 6801941, upload-time = "2025-06-26T16:20:45.765Z" }
wheels = [
    { url = "https://files.pythonhosted.org/packages/ed/20/f2b7ac96a91cc5f70d81320adad24cc41bf52013508d649b1481db225780/plotly-6.2.0-py3-none-any.whl", hash = "sha256:32c444d4c940887219cb80738317040363deefdfee4f354498cc0b6dab8978bd", size = 9635469, upload-time = "2025-06-26T16:20:40.76Z" },
]

[[package]]
name = "xquant-test"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "longport" },
    { name = "plotly" },
]

[package.metadata]
requires-dist = [
    { name = "longport", specifier = ">=3.0.8" },
    { name = "plotly", specifier = ">=6.2.0" },
]
