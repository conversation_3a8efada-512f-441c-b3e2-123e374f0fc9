# 互动经纪人

> 原文： [https://www.backtrader.com/docu/live/ib/ib/](https://www.backtrader.com/docu/live/ib/ib/)

与交互式代理的集成支持以下两个方面：

*   *现场数据*投料

*   *现场交易*

笔记

尽管所有的尝试都试图测试最大数量的错误条件和情况，但代码可能（像任何其他软件一样）包含 bug。

在投入生产之前，使用**纸面交易**账户或 TWS**演示**彻底测试任何策略。

笔记

通过使用`IbPy`模块与交互式代理进行交互，必须在使用前安装该模块。Pypi 中没有软件包（撰写本文时），但可以使用以下命令使用`pip`安装：

```py
pip install git+https://github.com/blampe/IbPy.git 
```

如果`git`在您的系统中不可用（Windows 安装？），则以下操作也应起作用：

```py
pip install https://github.com/blampe/IbPy/archive/master.zip 
```

## 示例代码

来源包含以下内容下的完整样本：

*   样本/ibtest/ibtest.py

该示例不能涵盖所有可能的用例，但它试图提供广泛的见解，并且应该强调，在使用回溯测试模块或实时数据模块时没有真正的区别

有一件事可以用别针指着：

*   在任何交易活动发生之前，样本等待`data.LIVE`数据状态通知。

    这可能是任何生活策略中需要考虑的问题。

## 商店模式与直接模式

通过两种模型支持与交互式代理的交互：

1.  门店型号（*首选*）

2.  与数据提要类和代理类的直接交互

门店模型在创建*经纪人*和*数据*时提供了清晰的分离模式。两个代码片段作为示例应该更好。

首先是**门店**车型：

```py
import backtrader as bt

ibstore = bt.stores.IBStore(host='127.0.0.1', port=7496, clientId=35)
data = ibstore.getdata(dataname='EUR.USD-CASH-IDEALPRO') 
```

以下是参数：

*   `host`、`port`和`clientId`被传递到`IBStore`所属的位置，后者使用这些参数打开连接。

然后使用`getdata`和*反向交易者*中所有数据馈送共用的参数创建**数据**馈送

*   `dataname`whic 要求提供*欧元*/*美元*外汇对。

现在直接使用：

```py
import backtrader as bt

data = bt.feeds.IBData(dataname='EUR.USD-CASH-IDEALPRO',
                       host='127.0.0.1', port=7496, clientId=35) 
```

在这里：

*   用于存储的参数将传递给数据。

*   这些将用于在后台创建一个`IBStore`实例

缺点是：

*   由于不清楚哪些属于数据，哪些属于存储，因此清晰度降低了很多。

## IBStore-商店

存储是实时数据馈送/交易支持的基石，在`IbPy`模块与数据馈送和代理的需求之间提供了一层自适应。

*商店*是一个涵盖以下功能的概念：

*   作为实体的中心商店：在这种情况下，实体是 IB

    可能需要也可能不需要参数

*   使用以下方法提供获取*broker*实例的访问权限：

    *   `IBStore.getbroker(*args, **kwargs)`
*   提供对 getter*数据*提要实例的访问

    *   `IBStore.getdata(*args, **kwargs)`

    在这种情况下，`**kwargs`中的许多内容与`dataname`、`fromdate`、`todate`、`sessionstart`、`sessionend`、`timeframe`、`compression`等数据源相同

    该数据可提供其他参数。检查下面的参考资料。

`IBStore`规定：

*   连通性目标（`host`和`port`参数）

*   标识（`clientId`参数）

*   重新连接控制（`reconnect`和`timeout`参数）

*   时间偏移检查（`timeoffset`参数，见下）

*   通知和调试

    `notifyall`（默认值：`False`：在这种情况下，IB 发送的任何`error`消息（许多只是信息性的）都会被中继到*大脑*/*策略*

    `_debug`（默认值：`False`：在这种情况下，从 TWS 接收到的每条消息都将打印到标准 outpu

## IBData 提要

### 数据选项

无论是直接还是通过`getdata`馈送，`IBData`馈送支持以下数据选项：

*   历史下载请求

    如果持续时间超过 IB 对给定的*时间段/压缩*组合施加的限制，则这些请求将被拆分为多个请求

*   3 种风格的实时数据

    *   `tickPrice`事件（通过 IB`reqMktData`）

    用于*现金*产品（至少使用 TWS API 9.70 进行的实验表明不支持其他类型）

    通过查看`BID`价格，收到*滴答*价格事件，根据非官方互联网文献，这似乎是跟踪`CASH`市场价格的方式。

    时间戳在系统中本地生成。如果最终用户愿意，可以使用 IB 服务器时间的偏移量（根据 IB`reqCurrentTime`计算）

    *   `tickString`事件（又名`RTVolume`（通过 IB`reqMktData`）

    大约每 250ms 从 IB 接收一次*OHLC/批量*快照（如果未发生交易，则更高）

    *   `RealTimeBars`事件（通过 IB`reqRealTimeBars`）

    每 5 秒接收历史 5 秒条形图（持续时间由 IB 固定）

    如果选择的*时间段/组合*低于*秒/5*水平，此功能将自动禁用。

    !!! 笔记

    ```py
     `RealTimeBars` do not work with the TWS Demo 
    ```

    默认行为是在大多数情况下使用：`tickString`，除非用户特别想要使用`RealTimeBars`

*   `Backfilling`

    除非用户请求仅进行*历史*下载，否则数据馈送将自动回填：

    *   **开始时**：最大可能持续时间。例如：对于*天/1*（*时间段/压缩*）组合，IB 的最大违约持续时间为*1 年*，这是将被回填的时间量

    *   **数据断开后**：在这种情况下，通过查看断开前收到的最新数据，回填操作下载的数据量将降至最低。

笔记

考虑到考虑的最终*时间帧/压缩*组合可能不是在*数据馈送创建*期间指定的组合，而是在系统中*插入*期间指定的组合。请参见以下示例：

```py
data = ibstore.getdata(dataname='EUR.USD-CASH-IDEALPRO',
                       timeframe=bt.TimeFrame.Seconds, compression=5)

cerebro.resampledata(data, timeframe=bt.TimeFrame.Minutes, compression=2) 
```

现在应该很清楚，考虑的最终*时间段/压缩*组合是*分钟/2*

### 数据契约检查

在启动阶段，*数据*提要将尝试下载指定合同的详细信息（如何指定请参见参考资料）。如果未找到此类合同或找到多个匹配项，则数据将拒绝继续并通知系统。一些例子。

简单但明确的合同规范：

```py
data = ibstore.getdata(dataname='TWTR')  # Twitter 
```

只会找到一个实例（2016-06），因为对于默认类型`STK`、交易所`SMART`和货币（默认为无），会找到在`USD`中交易的单个合约。

类似的方法将在`AAPL`中失败：

```py
data = ibstore.getdata(dataname='AAPL')  # Error -> multiple contracts 
```

因为`SMART`在多个实际交易所中找到合约，`AAPL`在其中一些交易所中以不同的货币进行交易。以下是确定的：

```py
data = ibstore.getdata(dataname='AAPL-STK-SMART-USD')  # 1 contract found 
```

### 数据通知

数据馈送将通过以下一项或多项报告当前状态（检查*大脑*和*策略*参考）

*   `Cerebro.notify_data`（如果被覆盖）n

*   添加了`Cerebro.adddatacb`的回调

*   `Strategy.notify_data`（如果被覆盖）

*策略*中的一个例子：

```py
class IBStrategy(bt.Strategy):

    def notify_data(self, data, status, *args, **kwargs):

        if status == data.LIVE:  # the data has switched to live data
           # do something
           pass 
```

系统中的以下更改将发送以下通知：

*   `CONNECTED`

    初始连接成功时发送

*   `DISCONNECTED`

    在这种情况下，无法再检索数据，数据将指示系统无法执行任何操作。可能的条件：

    *   指定了错误的合同

    *   历史下载期间的中断

    *   已超过尝试重新连接到 TWS 的次数

*   `CONNBROKEN`

    TWS 或数据场的连接已丢失。数据馈送将尝试（通过存储）在需要时重新连接和回填，并恢复操作

*   `NOTSUBSCRIBED`

    协定和连接正常，但由于缺少权限，无法检索数据。

    数据将向系统指示它无法检索数据

*   `DELAYED`

    信号表示*历史*/*回填*操作正在进行，策略处理的数据不是实时数据

*   `LIVE`

    表示*策略*从此点开始处理的数据为实时数据

Apple T0 策略的开发者应该考虑在断开发生时或当接收到 Ty2 T2 延迟的 T3 数据时采取哪些行动。

### 数据时间框架和压缩

*backtrader*生态系统中的数据馈送，在创建过程中支持`timeframe`和`compression`参数。这些参数也可以通过`data._timeframe`和`data._compression`作为属性访问

*时间帧/压缩*组合的重要性在通过`resampledata`或`replaydata`将数据传递给`cerebro`实例时有一个特定的目的，让内部重采样/重放对象了解预期目标是什么。重新采样/重放时，数据中将覆盖`._timeframe`和`._compression`。

但另一方面，在实时数据源中，这些信息可以发挥重要作用。请参见以下示例：

```py
data = ibstore.getdata(dataname='EUR.USD-CASH-IDEALPRO',
                       timeframe=bt.TimeFrame.Ticks,
                       compression=1,  # 1 is the default
                       rtbar=True,  # use RealTimeBars
                      )
cerebro.adddata(data) 
```

用户正在请求**勾选**数据，这很重要，因为：

*   不进行回填（IB 支持的最小单位为*秒/1*）

*   即使`dataname`请求并支持`RealTimeBars`，也不会使用，因为`RealTimeBar`的最小分辨率为*秒/5*

在任何情况下，除非使用*刻度/1*的分辨率，否则必须*重新采样/重放*。使用 realtimebars 和 working 的上述情况：

```py
data = ibstore.getdata(dataname='TWTR-STK-SMART', rtbar=True)
cerebro.resampledata(data, timeframe=bt.TimeFrame.Seconds, compression=20) 
```

在这种情况下，如上所述，数据的`._timeframe`和`._compression`属性将在`resampledata`期间被覆盖。这就是将要发生的事情：

*   *回填*需要*秒/20*的分辨率

*   `RealTimeBars`将用于实时数据，因为分辨率等于/大于*秒/5*且数据支持为（不是*现金*产品）

*   TWS 向系统发送的事件最多每 5 秒发生一次。这可能并不重要，因为系统只会每隔 20 秒向策略发送一个条。

无`RealTimeBars`时相同：

```py
data = ibstore.getdata(dataname='TWTR-STK-SMART')
cerebro.resampledata(data, timeframe=bt.TimeFrame.Seconds, compression=20) 
```

在这种情况下：

*   *回填*需要*秒/20*的分辨率

*   `tickString`将用于实时数据，因为（不是*现金*产品）

*   TWS 向系统发送的事件最多每 250ms 发生一次。这可能并不重要，因为系统只会每隔 20 秒向策略发送一个条。

最后使用*现金*产品，最长 20 秒：

```py
data = ibstore.getdata(dataname='EUR.USD-CASH-IDEALPRO')
cerebro.resampledata(data, timeframe=bt.TimeFrame.Seconds, compression=20) 
```

在这种情况下：

*   *回填*需要*秒/20*的分辨率

*   `tickPrice`将用于实时数据，因为这是现金产品

    即使添加了`rtbar=True`

*   TWS 向系统发送的事件最多每 250ms 发生一次。这可能并不重要，因为系统只会每隔 20 秒向策略发送一个条。

### 时间管理

数据馈送将根据*TWS*报告的`ContractDetails`对象自动确定时区。

笔记

这需要安装`pytz`。如果未安装，用户应向数据源提供所需输出时区的`tz`参数`tzinfo`兼容实例

笔记

如果安装了`pytz`并且用户感觉自动时区确定不起作用，`tz`参数可以包含一个带有时区名称的字符串。`backtrader`将尝试用给定名称实例化一个`pytz.timezone`

报告的`datetime`将是与产品相关的时区。一些例子：

*   *产品*：Eurex 中的 EuroStoxxx 50（股票代码：*ESTX50-YYYYMM-DTB*）

    时区为`CET`（*中欧时间*）又名`Europe/Berlin`

*   *产品*：ES 迷你（股票代码：*ES-YYYYMM-GLOBEX*）

    时区为`EST5EDT`aka`EST`aka`US/Eastern`

*   *产品*：欧元兑日元外汇对（股票代码*欧元兑日元-现金-IDEALPRO*）

    时区为`EST5EDT`aka`EST`aka`US/Eastern`

    实际上，这是一个交互式经纪人设置，因为外汇对交易几乎 24 小时不间断，因此不会有一个真正的时区。

考虑到计算机很可能具有实际位置时区而不是交易地点的时区，此行为确保交易保持一致，而与交易者的实际位置无关。

请阅读本手册的**时间管理**部分。

笔记

对于没有数据下载权限的资产，TWS 演示在报告时区时不准确（EuroStoxx 50 future 就是此类情况的一个例子）

### 实时提要和重采样/重播

关于何时交付带电进料棒的设计决策是：

*   *尽可能多地实时交付*

这可能看起来很明显，对于`Ticks`的时间段来说也是如此，但如果*重采样/重放*起作用，则可能会发生延迟。用例：

*   重采样配置为*秒/5*秒，具有：

    ```py
    cerebro.resampledata(data, timeframe=bt.TimeFrame.Seconds, compression=5) 
    ```

*   发送带有时间`23:05:27.325000`的勾号

*   市场交易缓慢，下一个滴答声在`23:05:59.025000`发出

这似乎不明显，但*反向交易者*不知道交易非常缓慢，下一个滴答声将在`32`秒后出现。如果没有规定，重新取样的时间为`23:05:30.000000`的酒吧将在`29 seconds`左右交付，这将太迟。

这就是为什么 live feed 每隔`x`秒（*浮点*值）唤醒一次，以转到*重采样器/重放器*，并让它知道没有新数据进入。这是在创建实时数据馈送时通过参数`qcheck`（默认值：`0.5`秒）控制的。

这意味着，如果本地时钟显示，重采样周期结束，重采样者有机会每`qcheck`秒发送一个酒吧。在此情况下，上述场景的重采样条（`23:05:30.000000`将在报告时间后最多`qcheck`秒发送。

因为默认值为`0.5`，所以最新时间为：`23:05:30.500000`。这几乎比以前提前了 29 秒。

缺点是：

*   *对于已经交付的重采样/重放条*而言，一些滴答声可能来得太晚

如果在交付后，TWS 收到来自服务器的时间戳为`23:05:29.995\`000`, this is simply too late for the already reported time to the system of`23:05.30.000000 的延迟消息`

这种情况主要发生在以下情况：

*   `IBStore\`中`timeoffset`*被禁用（设置为`False`），且*IB*报告时间与本地时钟之间的时差显著。

避免大多数延迟采样的最佳方法：

*   增加`qcheck`值，以考虑延迟消息：

    ```py
    data = ibstore.getdata('TWTR', qcheck=2.0, ...) 
    ```

这将增加额外的空间，即使它延迟了*重采样/重放*条的交付

笔记

当然，2.0 秒的延迟对于*秒/5*重采样的意义不同于*分钟/10*重采样的意义

如果最终用户出于任何原因希望禁用`timeoffset`而不通过`qcheck`进行管理，则仍然可以采集延迟样本：

*   使用`_latethrough`设置为`True`作为`getdata`/`IBData`的参数：

    ```py
    data = ibstore.getdata('TWTR', _latethrough=True, ...) 
    ```

*   当*重采样/重放*时，使用`takelate`设置为`True`：

    ```py
    cerebro.resampledata(data, takelate=True) 
    ```

## IBBroker-实时交易

笔记

根据请求，在*backtrader*中提供的*经纪人模拟*中实现了`tradeid`功能。这允许跟踪在同一资产上并行执行的交易，并将佣金正确分配给相应的`tradeid`

这种想法在这个实时经纪人中不受支持，因为经纪人报告佣金时，不可能将佣金与不同的`tradeid`值分开。

`tradeid`仍然可以指定，但不再有意义。

### 使用代理

要使用*IB 代理*，必须替换*大脑*创建的标准代理模拟实例。

使用*商店*型号（首选）：

```py
import backtrader as bt

cerebro = bt.Cerebro()
ibstore = bt.stores.IBStore(host='127.0.0.1', port=7496, clientId=35)
cerebro.broker = ibstore.getbroker()  # or cerebro.setbroker(...) 
```

使用直接方法：

```py
import backtrader as bt

cerebro = bt.Cerebro()
cerebro.broker = bt.brokers.IBBroker(host='127.0.0.1', port=7496, clientId=35) 
```

### 代理参数

无论是直接还是通过`getbroker`代理`IBBroker`都不支持任何参数。这是因为经纪人只是真实的*经纪人*的代理。而真正的经纪人所给予的，不应被剥夺。

### 一些限制

#### 现金和价值报告

如果内部*反向交易者*经纪人模拟在调用策略`next`方法之前对`value`（净清算价值）和`cash`进行了计算，那么对于一个活跃的经纪人来说，这是无法保证的。

*   如果请求了这些值，`next`的执行可能会延迟到答案到达

*   经纪人可能还没有计算出这些值

*backtrader*告知 TWS 在更新后立即提供更新值（*backtrader*订阅`accounUpdate`消息），但不知道消息何时到达。

`IBBroker`的`getcash`和`getvalue`方法报告的值始终是从 IB 收到的最新值。

笔记

另一个限制是，即使有更多货币的值可用，也以账户的基础货币报告值。这是一个设计选择。

#### 位置

*反向交易者*使用 TWS 报告的资产的`Position`（价格和规模）。内部计算可在*订单执行*和*订单状态*消息后使用，但如果其中一些消息丢失（套接字有时丢失数据包），则不会进行计算。

当然，如果在连接到 TWS 时，将执行交易的资产已经有未平仓头寸，则由于初始抵销，该策略所做的`Trades`计算将无法正常工作

### 用它交易

标准用法没有变化。只需使用策略中可用的方法（完整解释请参见`Strategy`参考）

*   `buy`

*   `sell`

*   `close`

*   `cancel`

### 返回的订单对象

*   与 backtrader`Order`对象兼容（同一层次中的子类）

### 订单执行类型

IB 支持多种执行类型，其中一些由 IB 模拟，另一些由 exchange 本身支持。关于最初支持哪种订单执行类型的决定有一个动机：

*   与*反向交易者*中提供的*经纪人模拟*的兼容性

    其理由是，经过反向测试的产品将投入生产。

因此，订单执行类型仅限于*经纪人模拟*中可用的类型：

*   `Order.Market`

*   `Order.Close`

*   `Order.Limit`

*   `Order.Stop`（触发*停止*时，跟随*市场*指令）

*   `Order.StopLimit`（触发*停止*时，跟随*限制*指令）

笔记

IB 按照不同的策略进行停止触发。*反向交易者*不修改默认设置`0`：

```py
0 - the default value. The "double bid/ask" method will be used for
orders for OTC stocks and US options. All other orders will use the
"last" method. 
```

如果用户希望对此进行修改，可根据 IB 文档向`buy`和`sell`提供额外的`**kwargs`。例如，在策略的`next`方法中：

```py
def next(self):
    # some logic before
    self.buy(data, m_triggerMethod=2) 
```

这将策略更改为`2`（*“上次”方式，根据上次价格触发停单。*

有关停止触发的任何进一步说明，请参阅 IB API 文档

### 订单有效期

回溯测试中可用的相同有效性概念（具有`valid`至`buy`和`sell`的有效性概念）可用且具有相同的含义。因此，对于*IB 订单*，将`valid`参数转换为以下值：

*   `None -> GTC`（良好，直至取消）

    由于未指定有效期，因此订单必须在取消前有效

*   `datetime/date`翻译为`GTD`（截止日期前有效）

    传递 datetime.datetime/datetime.date 实例表示订单在给定时间点之前必须有效。

*   `timedelta(x)`翻译为`GTD`（此处为`timedelta(x) != timedelta()`）

    这被解释为命令从`now`+`timedelta(x)`开始生效的信号

*   `float`翻译成`GTD`

    如果该值取自*反向交易者*使用的原始*浮动*日期时间存储器，则订单必须在*浮动*指示的日期时间之前有效

*   `timedelta() or 0`翻译成`DAY`

    一个值已被删除（而不是`None`），但为*空*，并被解释为对当前*天*（会话）有效的订单

### 通知

标准`Order`状态将通过`notify_order`方法通知*策略*（如果被覆盖）

*   `Submitted`-订单已发送至 TWS

*   `Accepted`-已下订单

*   `Rejected`-下单失败或在其有效期内被系统取消

*   `Partial`-部分执行已发生

*   `Completed`-订单已全部执行

*   `Canceled`（或`Cancelled`）

    这在 IB 下有几个含义：

    *   手动用户取消

    *   服务器/交易所取消了订单

    *   订单有效期到期

        将应用启发式，如果从 TWS 接收到一条`openOrder`消息，其中`orderState`指示`PendingCancel`或`Canceled`，则订单将标记为`Expired`

*   `Expired`-请参见上面的说明

## 参考

### 易博商店

#### 类 backtrader.stores.IBStore（）

包装 ibpy ibConnection 实例的 Singleton 类。

也可以在使用此存储的类中指定参数，如`IBData`和`IBBroker`

参数：

*   `host`（默认值：`127.0.0.1`：IB TWS 或 IB 网关实际运行的位置。虽然这通常是本地主机，但不能是本地主机

*   `port`（默认值：`7496`：要连接的端口。演示系统使用`7497`

*   `clientId`（默认值：`None`：用于连接 TWS 的客户端 ID。

    `None`：生成一个介于 1 和 65535 之间的随机 id，`integer`：将作为要使用的值传递。

*   `notifyall`（默认为`False`）

    如果`False`只发送`error`消息到`Cerebro`和`Strategy`的`notify_store`方法。

    如果`True`，则会通知从 TWS 收到的每条消息

*   `_debug`（默认为`False`）

    将从 TWS 接收的所有消息打印到标准输出

*   `reconnect`（默认为`3`）

    在 1<sup>st</sup>连接尝试失败后尝试重新连接的次数

    将其设置为`-1`值以保持永久重新连接

*   `timeout`（默认为`3.0`）

    重新连接尝试之间的时间（秒）

*   `timeoffset`（默认为`True`）

    如果为 True，则从`reqCurrentTime`（IB 服务器时间）获得的时间将用于计算到 localtime 的偏移量，该偏移量将用于价格通知（例如，对于现金市场）以修改本地计算的时间戳。

    时间偏移将传播到`backtrader`生态系统的其他部分，如**重采样**，以使用计算出的偏移对齐重采样时间戳。

*   `timerefresh`（默认为`60.0`）

    以秒为单位的时间：刷新时间偏移的频率

*   `indcash`（默认为`True`）

    管理 IND 代码，就好像它们是用于价格检索的现金一样

### IBBroker

#### 类 backtrader.brokers.IBBroker（**kwargs）

交互式代理的代理实现。

此类将交互经纪人的订单/头寸映射到`backtrader`的内部 API。

### 笔记

*   `tradeid`实际上不受支持，因为损益直接来自 IB。因为（如预期）以 FIFO 方式计算，pnl 对于 tradeid 不准确。

*   位置

如果在操作开始时存在资产的未平仓或通过其他方式发出的指令改变仓位，则在 Cerbero 的`Strategy`中计算的交易将不会反映实际情况。

为了避免这种情况，该经纪人必须进行自己的头寸管理，这也将允许 tradeid 使用多个 ID（利润和损失也将在本地计算），但可能会被视为违背与实时经纪人合作的目的

### IBData

#### 类 backtrader.feeds.IBData（**kwargs）

交互式代理数据源。

参数`dataname`中支持以下合同规范：

*   股票代码#股票类型和智能交易所

*   TICKER-STK#股票和智能交易所

*   股票交易所

*   TICKER-STK-EXCHANGE-CURRENCY#Stock

*   TICKER-CFD#CFD 和智能交换

*   股票代码-CFD-EXCHANGE#CFD

*   TICKER-CDF-EXCHANGE-CURRENCY#Stock

*   股票交易所指数

*   TICKER-IND-EXCHANGE-CURRENCY 指数

*   TICKER-YYYYMM-EXCHANGE#未来

*   TICKER-YYYYMM-EXCHANGE-CURRENCY#未来

*   TICKER-YYYYMM-EXCHANGE-CURRENCY-MULT#Future

*   TICKER-FUT-EXCHANGE-CURRENCY-YYYYMM-MULT#未来

*   TICKER-YYYYMM-EXCHANGE-CURRENCY-STRIKE-RIGHT#FOP

*   TICKER-YYYYMM-EXCHANGE-CURRENCY-STRIKE-RIGHT-MULT#FOP

*   TICKER-FOP-EXCHANGE-CURRENCY-YYYYMM-STRIKE-RIGHT#FOP

*   TICKER-FOP-EXCHANGE-CURRENCY-YYYYMM-STRIKE-RIGHT-MULT#FOP

*   CUR1.CUR2-CASH-IDEALPRO#外汇

*   TICKER-YYYYMMDD-EXCHANGE-CURRENCY-STRIKE-RIGHT#OPT

*   TICKER-YYYYMMDD-EXCHANGE-CURRENCY-STRIKE-RIGHT-MULT#OPT

*   TICKER-OPT-EXCHANGE-CURRENCY-YYYYMMDD-STRIKE-RIGHT#OPT

*   TICKER-OPT-EXCHANGE-CURRENCY-YYYYMMDD-STRIKE-RIGHT-MULT#OPT

参数：

*   `sectype`（默认为`STK`）

    如果`dataname`规范中未提供，则应用为*安全类型*的默认值

*   `exchange`（默认为`SMART`）

    如果`dataname`规范中未提供，则应用为*交换*的默认值

*   `currency`（默认为`''`）

    如果`dataname`规范中未提供，则默认值应用为*货币*

*   `historical`（默认为`False`）

    如果设置为`True`，数据馈送将在第一次下载数据后停止。

    标准数据馈送参数`fromdate`和`todate`将用作参考。

    如果请求的持续时间大于 IB 允许的持续时间，则数据馈送将发出多个请求，前提是为数据选择了时间段/压缩。

*   `what`（默认为`None`）

    如果`None`不同资产类型的默认值将用于历史数据请求：

    *   “竞购”现金资产

    *   “交易”任何其他

    如果需要其他值，请检查 IB API 文档

*   `rtbar`（默认为`False`）

    如果`True`将使用互动经纪人提供的`5 Seconds Realtime bars`作为 smalles 勾号。根据文档，它们对应于实时值（一旦 IB 整理和管理）

    如果是`False`，则将使用`RTVolume`价格，该价格基于收到的滴答声。在`CASH`资产的情况下（例如，欧元兑日元）`RTVolume`将始终使用，并从中获得`bid`价格（根据分散在互联网上的文献，IB 的行业事实标准）

    即使设置为`True`，如果数据被重新采样/保存到低于秒/5 的时间段/压缩，也不会使用实时条，因为 IB 不会在低于该水平的情况下为其提供服务

*   `qcheck`（默认为`0.5`）

    如果没有接收到数据，以秒为单位的唤醒时间，以便有机会正确地重新采样/重播数据包，并在链上传递通知

*   `backfill_start`（默认为`True`）

    开始时进行回填。在单个请求中将获取最大可能的历史数据。

*   `backfill`（默认为`True`）

    断开/重新连接循环后进行回填。间隙持续时间将用于下载尽可能少的数据量

*   `backfill_from`（默认为`None`）

    可以传递一个额外的数据源来进行初始层的回填。一旦数据源耗尽，如果需要，将从 IB 进行回填。理想情况下，这意味着要从已存储的源（如磁盘上的文件）进行回填，但不限于此。

*   `latethrough`（默认为`False`）

    如果对数据源进行了重采样/重放，则对于已交付的重采样/重放条，某些标记可能来得太迟。如果这是`True`，那么这些滴答声在任何情况下都会通过。

    查看重采样器文档，看看谁应该考虑这些滴答声。

    如果在`IBStore`实例中将`timeoffset`设置为`False`并且 TWS 服务器时间与本地计算机的时间不同步，则可能会发生这种情况

*   `tradename`（默认值：`None`）适用于`CFD`等特定情况，其中价格由一种资产提供，交易在另一种资产中进行

    *   SPY-STK-SMART-USD->SP500 ETF（将指定为`dataname`）

    *   SPY-CFD-SMART-USD->这是相应的 CFD，不提供价格跟踪，但在这种情况下将是交易资产（指定为`tradename`）

参数中的默认值是允许应用`\`TICKER`, to which the parameter`sectype`(default:`STK`) and`exchange`(default:`SMART`）之类的内容。

像`AAPL`这样的一些资产需要完整的规范，包括`currency`（默认值：“”），而像`TWTR`这样的其他资产可以简单地按原样传递。

*   `AAPL-STK-SMART-USD`将是 dataname 的完整规范

    或者：`IBData`为`IBData(dataname='AAPL', currency='USD')`，使用默认值（`STK`和`SMART`，并将币种改写为`USD`