# 策略参考

> 原文： [https://www.backtrader.com/docu/strategy-reference/](https://www.backtrader.com/docu/strategy-reference/)

内置策略的参考

### 马乌渡线

别名：

```py
* SMA_CrossOver 
```

这是一种只在移动平均线交叉点上操作的多头策略

注:

```py
* Although the default 
```

购买逻辑：

```py
* No position is open on the data

* The `fast` moving averagecrosses over the `slow` strategy to the
  upside. 
```

销售逻辑：

```py
* A position exists on the data

* The `fast` moving average crosses over the `slow` strategy to the
  downside 
```

订单执行类型：

```py
* Market 
```

线：

```py
* datetime 
```

参数：

```py
* fast (10)

* slow (30)

* _movav (<class ‘backtrader.indicators.sma.SMA’>) 
```

### 信号策略

`Strategy`的子类用于使用**信号**进行自动操作。

*信号*通常是指标和期望输出值：

*   `> 0`为`long`指示

*   `< 0`为`short`指示

有 5 种类型的*信号*，分为 2 组。

**主要组别**：

*   `LONGSHORT`：此信号的`long`和`short`指示均已获取

*   `LONG`：

    *   `long`迹象被认为是长期的
    *   `short`指示*关闭*多头位置。但是：

    *   如果系统中存在`LONGEXIT`（见下文）信号，则该信号将用于退出长时间运行

    *   如果有`SHORT`信号，但没有`LONGEXIT`信号，则在打开 a`short`之前先关闭 a`long`

*   `SHORT`：

    *   `short`迹象被认为是做空
    *   `long`指示*关闭*空头仓位。但是：

    *   如果系统中存在`SHORTEXIT`（见下文）信号，则该信号将用于退出短路

    *   如果有`LONG`信号，但没有`SHORTEXIT`信号，则在打开 a`long`之前先关闭 a`short`

**退出组**：

这两个信号旨在覆盖其他信号，并为存在的 a`long`/`short`位置提供标准

*   `LONGEXIT`：`short`显示退出`long`位置

*   `SHORTEXIT`：`long`显示退出`short`位置

**下单**

订单执行类型为`Market`，有效期为`None`（*取消前有效*）

参数：

*   `signals`（默认值：`[]`：列表/元组的列表/元组，允许实例化信号并分配到正确的类型

    此参数预计通过`cerebro.add_signal`进行管理

*   `_accumulate`（默认值：`False`：允许进入市场（长/短），即使已经进入市场

*   `_concurrent`（默认值：`False`：即使订单已经挂起执行，也允许发出订单

*   `_data`（默认值：`None`：如果订单目标系统中存在多个数据。这可能是

    *   `None`：将使用系统中的第一个数据

    *   `int`：表示在该位置插入的数据

    *   `str`：创建数据（参数`name`）或将数据添加到`cerebro.adddata(..., name=)`时给出的名称

    *   `data`实例

线：

```py
* datetime 
```

参数：

```py
* signals ([])

* _accumulate (False)

* _concurrent (False)

* _data (None) 
```