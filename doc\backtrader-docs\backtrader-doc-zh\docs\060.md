# 经纪人

> 原文： [https://www.backtrader.com/docu/broker/](https://www.backtrader.com/docu/broker/)

## 参考

### 类 backtrader.brokers.BackBroker（）

经纪人模拟器

模拟支持不同的订单类型，根据当前现金检查提交的订单现金需求，跟踪`cerebro`每次迭代的现金和价值，并在不同数据上保持当前位置。

*现金*在`futures`等工具的每次迭代中进行调整

```py
which a price change implies in real brokers the addition/substracion of
cash. 
```

支持的订单类型：

*   `Market`：以下一条<sup>st</sup>勾号执行（即`open`价格）

*   `Close`：指以交易日最后一条收盘价执行订单的当天

*   `Limit`：如果在会话期间看到给定的限价，则执行

*   `Stop`：如果看到给定的止损价，则执行`Market`指令

*   `StopLimit`：如果看到给定的止损价，则设置一个`Limit`运动指令

由于代理是由`Cerebro`实例化的，并且（大多数情况下）没有理由替换代理，因此实例的参数不受用户控制。要更改此设置，有两个选项：

1.  使用所需参数手动创建该类的实例，并使用`cerebro.broker = instance`将该实例设置为`run`执行的代理

2.  使用`set_xxx`使用`cerebro.broker.set_xxx`设置值，其中`\`xxx`表示要设置的参数的名称

笔记

`cerebro.broker`是`Cerebro`的`getbroker`和`setbroker`方法支持的*属性*

参数：

*   `cash`（默认为`10000`：起始现金

*   `commission`（默认为`CommInfoBase(percabs=True)`）适用于所有资产的基础佣金方案

*   `checksubmit`（默认值：`True`）在接受系统订单之前检查保证金/现金

*   默认情况：默认值：OUTT1）：随着日内酒吧考虑一个酒吧相同的 T2 T2。通常情况并非如此，因为一些酒吧（最终拍卖）是由许多交易所在交易结束后几分钟内为许多产品制作的

*   默认情况：默认值：OUTT1）：随着日内酒吧考虑一个酒吧相同的 T2 T2。通常情况并非如此，因为一些酒吧（最终拍卖）是由许多交易所在交易结束后几分钟内为许多产品制作的

*   `filler`（默认为`None`）

    可调用的签名：`callable(order, price, ago)`

    *   `order`：显然是执行中的命令。这提供了对*数据*（以及*ohlc*和*卷*值）、*执行类型*、剩余大小（`order.executed.remsize`等）的访问。

        请查看`Order`文档和参考资料，以了解`Order`实例中可用的内容

    *   `price`在`ago`栏中执行订单的价格

    *   `ago`：与`order.data`一起用于提取*ohlc*和*量*价格的指数。在大多数情况下，这将是`0`，但在`Close`订单的拐角处，这将是`-1`。

        为了获得棒体积（例如），执行：`volume = order.data.voluume[ago]`

    可调用方必须返回*执行大小*（值>=0）

    当然，可调用对象可能是一个具有`__call__`匹配上述签名的对象

    默认情况下，`None`命令将在一次射击中完全执行

*   `slip_perc`（默认值：`0.0`）绝对术语的百分比（和正值），应用于推高/下调买入/卖出订单的价格

    注:

    *   `0.01`是`1%`

    *   `0.001`是`0.1%`

*   `slip_fixed`（默认值：`0.0`）单位百分比（和正值），用于买卖订单上/下滑动价格

    注：若`slip_perc`为非零，则其先于此。

*   `slip_open`（默认值：`False`）订单执行是否打滑，具体使用下一条*期初*价格。一个例子是`Market`订单，该订单使用下一个可用的勾号执行，即：酒吧的开盘价。

    这也适用于其他一些执行，因为当移动到新条时，逻辑试图检测*期初*价格是否与请求的价格/执行类型匹配。

*   `slip_match`（默认为`True`）

    如果`True`经纪人将提供匹配，以`high/low`价格封顶滑动，以防超过。

    如果`False`经纪人不会将订单与当前价格匹配，并将在下一次迭代中尝试执行

*   `slip_limit`（默认为`True`）

    `Limit`即使`slip_match`为`False`也会根据要求的准确匹配价格匹配订单。

    此选项控制该行为。

    如果为`True`，则`Limit`订单将通过封顶价格与`limit`/`high/low`价格匹配

    如果`False`和滑动超过上限，则将不存在匹配

*   `slip_out`（默认为`False`）

    即使价格在`high`-`low`范围之外，也要提供*打滑*。

*   `coc`（默认为`False`）

    *关闭时作弊*通过`set_coc`将其设置为`True`启用

    ```py
    matching a `Market` order to the closing price of the bar in which
    the order was issued. This is actually *cheating*, because the bar
    is *closed* and any order should first be matched against the prices
    in the next bar 
    ```

*   `coo`（默认为`False`）

    *开启时作弊*通过`set_coo`将其设置为`True`启用

    ```py
    matching a `Market` order to the opening price, by for example
    using a timer with `cheat` set to `True`, because such a timer
    gets executed before the broker has evaluated 
    ```

*   `int2pnl`（默认为`True`）

    将产生的利息（如有）分配给减少头寸（无论是多头还是空头）的经营损益。在某些情况下，这是不希望的，因为不同的战略相互竞争，利益将在不确定的基础上分配给它们中的任何一个。

*   `shortcash`（默认为`True`）

    如果为真，则当股票型资产做空时，现金将增加，资产的计算值将为负值。

    如果`False`，则现金将作为运营成本扣除，计算值将为正，以相同金额结束

*   `fundstartval`（默认为`100.0`）

    此参数控制以类似基金的方式衡量绩效的起始值，即：增加股票金额可以增加和减少现金。业绩不是用 porftoflio 的资产净值来衡量的，而是用基金的价值来衡量的

*   `fundmode`（默认为`False`）

    如果设置为`True`，类似`TimeReturn`的分析器可以根据基金价值而不是总资产净值自动计算回报

### 套现（现金）

设置现金参数（别名：`setcash`

### 获得现金

返回当前现金（别名：`getcash`

### 获取 _ 值（数据=None，mkt=False，lever=False）

返回给定数据的组合值（如果数据为`None`，则返回组合总值（别名：`getvalue`）

### 设置 eosbar（eosbar）

设置 eosbar 参数（别名：`seteosbar`

### 设置检查提交（检查提交）

设置 checksubmit 参数

### 设置填料（填料）

为卷填充执行设置卷填充

### 设置 coc（coc）

配置关闭时作弊方法以购买订单栏上的关闭

### 设置首席运营官（首席运营官）

配置打开时作弊方法以购买订单栏上的关闭

### set_int2pnl（int2pnl）

将权益分配配置为损益

### set_fundstartval（fundstartval）

设置基金式绩效跟踪系统的起始值

### 设置滑动 perc（perc，滑动打开=真，滑动限制=真，滑动匹配=真，滑动退出=假）

将滑动配置为基于百分比

### 设置滑动固定（固定，滑动打开=真，滑动限制=真，滑动匹配=真，滑动退出=假）

将滑动配置为基于固定点

### 打开订单（安全=错误）

返回一个 iterable，其中的订单仍处于打开状态（未执行或部分执行）

不得触碰退回的订单。

如果需要进行订单操作，则将参数`safe`设置为 True

### getcommissioninfo（数据）

检索与给定`data`关联的`CommissionInfo`方案

### setcommission（佣金=0.0，保证金=None，mult=1.0，commtype=None，percabs=True，股票型=False，利息=0.0，利息=False，杠杆=1.0，自动保证金=False，名称=None）

此方法使用参数为代理中管理的资产设置“CommissionInfo”对象。参考`CommInfoBase`的参考资料

如果名称为`None`，则找不到其他`CommissionInfo`方案的资产默认为该名称

### addcommissioninfo（comminfo，name=None）

添加一个`CommissionInfo`对象，如果`name`为`None`，则该对象将是所有资产的默认对象

### getposition（数据）

返回给定`data`的当前位置状态（一个`Position`实例）

### 获得基金份额

返回类基金模式下的当前股数

### 获取资金价值（）

返回类似于基金的股票价值

### 加现金（现金）

向系统添加/删除现金（使用负值删除）