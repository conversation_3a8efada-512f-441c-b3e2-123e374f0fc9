# 第 1.2.1.88 版

> 原文： [https://www.backtrader.com/blog/posts/2016-03-07-release-1.2.1.88/release-1.2.1.88/](https://www.backtrader.com/blog/posts/2016-03-07-release-1.2.1.88/release-1.2.1.88/)

将次要版本号从 1 更改为 2 需要一些时间，但旧 DataResampler 和 DataReplayer 的弃用导致了这一问题。

**已阅读 DOCS**的文件

[文档](http://backtrader.readthedocs.org/en/latest/)已更新为仅参考现代方式`resampling`和`replaying`。这很容易：

```py
...
data = backtrader.feeds.BacktraderCSVData(dataname='mydata.csv')  # daily bars
cerebro.resampledata(data, timeframe=backtrader.TimeFrame.Weeks) # to weeks
... 
```

对于*重播*只需将`resampledata`更改为`replaydata`。还有其他方法可以实现，但这是最直接的界面，可能是唯一一个被任何人使用的界面。

在[票证【60】之后，很明显，允许向数据馈送添加额外行的扩展机制（实际上是向任何基于*行*的对象）不足以支持票证中建议的内容。](https://github.com/mementum/backtrader/issues/60)

因此，在*线条*对象上增加了一个*参数*，该参数允许线条层次结构的完全重新定义（**逃离 OHLC 土地**将是一个合适的电影标题）

源中添加了一个名为*数据 bid ask*的样本。从样本中：

```py
class BidAskCSV(btfeeds.GenericCSVData):
    linesoverride = True  # discard usual OHLC structure
    # datetime must be present and last
    lines = ('bid', 'ask', 'datetime')
    # datetime (always 1st) and then the desired order for
    params = (
        ('dtformat', '%m/%d/%Y %H:%M:%S'),

        ('datetime', 0),  # field pos 0
        ('bid', 1),  # default field pos 1
        ('ask', 2),  # defult field pos 2
    ) 
```

通过指定`linesoverride`规则*行*继承机制被绕过，对象中定义的行将取代之前的任何行。

该释放装置可从*pypi*处获得，并可通过以下方式安装：

```py
pip install backtrader 
```

或者，如果更新：

```py
pip install backtrader --upgrade 
```