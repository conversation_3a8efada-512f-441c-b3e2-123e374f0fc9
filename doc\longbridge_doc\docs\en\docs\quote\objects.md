---
sidebar_position: 2
id: quote_object
title: Definition
slug: objects
---

## TradeStatus - Security Status

Security Status

| ID  | Description         |
| --- | ------------------- |
| 0   | Normal              |
| 1   | Suspension          |
| 2   | Delisted            |
| 3   | Fuse                |
| 4   | Papare List         |
| 5   | Code Moved          |
| 6   | To Be Opened        |
| 7   | Split Stock Halts   |
| 8   | Expired             |
| 9   | Warrant To BeListed |
| 10  | Suspend             |

### Protobuf

```protobuf
enum TradeStatus {
  NORMAL = 0;
  HALTED = 1;
  DELISTED = 2;
  FUSE = 3;
  PREPARE_LIST = 4;
  CODE_MOVED = 5;
  TO_BE_OPENED = 6;
  SPLIT_STOCK_HALTS = 7;
  EXPIRED = 8;
  WARRANT_PREPARE_LIST = 9;
  SUSPEND_TRADE = 10;
}
```

## TradeSession - Trading Session

Trading Session

| ID  | Description       |
| --- | ----------------- |
| 0   | Trading           |
| 1   | Pre-Tradeing      |
| 2   | Post-Tradeing     |
| 3   | OverNight-Trading |

### Protobuf

```protobuf
enum TradeSession {
  NORMAL_TRADE = 0;
  PRE_TRADE = 1;
  POST_TRADE = 2;
}
```

## Period - Candlestick Period

| ID   | Description        |
| ---- | ------------------ |
| 1    | One Minute         |
| 2    | Two Minutes        |
| 3    | Three Minutes      |
| 5    | Five Minutes       |
| 10   | Ten Minutes        |
| 15   | Fifteen Minutes    |
| 20   | Twenty Minutes     |
| 30   | Thirty Minutes     |
| 45   | Forty-five Minutes |
| 60   | Sixty Minutes      |
| 120  | Two Hours          |
| 180  | Three Hours        |
| 240  | Four Hours         |
| 1000 | One Days           |
| 2000 | One Week           |
| 3000 | One Month          |
| 3500 | One Quarter        |
| 4000 | One Year           |

### Protobuf

```protobuf
enum Period {
  UNKNOWN_PERIOD = 0;
  ONE_MINUTE = 1;
  FIVE_MINUTE = 5;
  FIFTEEN_MINUTE = 15;
  THIRTY_MINUTE = 30;
  SIXTY_MINUTE = 60;
  DAY = 1000;
  WEEK = 2000;
  MONTH = 3000;
  YEAR = 4000;
}
```

## AdjustType - Candlestick Adjustment Type

| ID  | Description    |
| --- | -------------- |
| 0   | Actual         |
| 1   | Adjust forward |

### Protobuf

```protobuf
enum AdjustType {
  NO_ADJUST = 0;
  FORWARD_ADJUST = 1;
}
```

## SubType - Quote Type Of Subscription

| ID  | Description |
| --- | ----------- |
| 1   | Quote       |
| 2   | Depth       |
| 3   | Broker      |
| 4   | Trade       |

### Protobuf

```protobuf
enum SubType {
  UNKNOWN_TYPE = 0;
  QUOTE = 1;
  DEPTH = 2;
  BROKERS = 3;
  TRADE = 4;
}
```

## CalcIndex - Calculate Index

| ID  | Description                        | Applicable Security Type |
| --- | ---------------------------------- | ------------------------ |
| 1   | Latest price                       | All                      |
| 2   | Change value                       | All                      |
| 3   | Change ratio                       | All                      |
| 4   | Volume                             | All                      |
| 5   | Turnover                           | All                      |
| 6   | Year-to-date change ratio          | Except Option, Warrant   |
| 7   | Turnover rate                      | Except Option, Warrant   |
| 8   | Total market value                 | Except Option, Warrant   |
| 9   | Capital flow                       | Except Option, Warrant   |
| 10  | Amplitude                          | Except Option, Warrant   |
| 11  | Volume ratio                       | Except Option, Warrant   |
| 12  | PE (TTM)                           | Except Option, Warrant   |
| 13  | PB                                 | Except Option, Warrant   |
| 14  | Dividend ratio (TTM)               | Except Option, Warrant   |
| 15  | Five days change ratio             | Except Option, Warrant   |
| 16  | Ten days change ratio              | Except Option, Warrant   |
| 17  | Half year change ratio             | Except Option, Warrant   |
| 18  | Five minutes change ratio          | Except Option, Warrant   |
| 19  | Expiry date                        | Only Option, Warrant     |
| 20  | Strike Price                       | Only Option, Warrant     |
| 21  | Upper bound price                  | Only Warrant             |
| 22  | Lower bound price                  | Only Warrant             |
| 23  | Outstanding quantity               | Only Warrant             |
| 24  | Outstanding ratio                  | Only Warrant             |
| 25  | Premium                            | Only Option, Warrant     |
| 26  | In/out of the bound                | Only Warrant             |
| 27  | Implied volatility                 | Only Option, Warrant     |
| 28  | Warrant delta                      | Only Warrant             |
| 29  | Call price                         | Only Warrant             |
| 30  | Price interval from the call price | Only Warrant             |
| 31  | Effective leverage                 | Only Warrant             |
| 32  | Leverage ratio                     | Only Warrant             |
| 33  | Conversion ratio                   | Only Warrant             |
| 34  | Breakeven point                    | Only Warrant             |
| 35  | Open interest                      | Only Option              |
| 36  | Delta                              | Only Option              |
| 37  | Gamma                              | Only Option              |
| 38  | Theta                              | Only Option              |
| 39  | Vega                               | Only Option              |
| 40  | Rho                                | Only Option              |

### Protobuf

```protobuf
enum CalcIndex {
  CALCINDEX_UNKNOWN = 0;
  CALCINDEX_LAST_DONE = 1;
  CALCINDEX_CHANGE_VAL = 2;
  CALCINDEX_CHANGE_RATE = 3;
  CALCINDEX_VOLUME = 4;
  CALCINDEX_TURNOVER = 5;
  CALCINDEX_YTD_CHANGE_RATE = 6;
  CALCINDEX_TURNOVER_RATE = 7;
  CALCINDEX_TOTAL_MARKET_VALUE = 8;
  CALCINDEX_CAPITAL_FLOW = 9;
  CALCINDEX_AMPLITUDE = 10;
  CALCINDEX_VOLUME_RATIO = 11;
  CALCINDEX_PE_TTM_RATIO = 12;
  CALCINDEX_PB_RATIO = 13;
  CALCINDEX_DIVIDEND_RATIO_TTM = 14;
  CALCINDEX_FIVE_DAY_CHANGE_RATE = 15;
  CALCINDEX_TEN_DAY_CHANGE_RATE = 16;
  CALCINDEX_HALF_YEAR_CHANGE_RATE = 17;
  CALCINDEX_FIVE_MINUTES_CHANGE_RATE = 18;
  CALCINDEX_EXPIRY_DATE = 19;
  CALCINDEX_STRIKE_PRICE = 20;
  CALCINDEX_UPPER_STRIKE_PRICE = 21;
  CALCINDEX_LOWER_STRIKE_PRICE = 22;
  CALCINDEX_OUTSTANDING_QTY = 23;
  CALCINDEX_OUTSTANDING_RATIO = 24;
  CALCINDEX_PREMIUM = 25;
  CALCINDEX_ITM_OTM = 26;
  CALCINDEX_IMPLIED_VOLATILITY = 27;
  CALCINDEX_WARRANT_DELTA = 28;
  CALCINDEX_CALL_PRICE = 29;
  CALCINDEX_TO_CALL_PRICE = 30;
  CALCINDEX_EFFECTIVE_LEVERAGE = 31;
  CALCINDEX_LEVERAGE_RATIO = 32;
  CALCINDEX_CONVERSION_RATIO = 33;
  CALCINDEX_BALANCE_POINT = 34;
  CALCINDEX_OPEN_INTEREST = 35;
  CALCINDEX_DELTA = 36;
  CALCINDEX_GAMMA = 37;
  CALCINDEX_THETA = 38;
  CALCINDEX_VEGA = 39;
  CALCINDEX_RHO = 40;
}
```

## Board - Security Board

| Board            | Description                                   |
| ---------------- | --------------------------------------------- |
| USMain           | US Main Board                                 |
| USPink           | US Pink Board                                 |
| USDJI            | Dow Jones Industrial Average                  |
| USNSDQ           | Nasdsaq Index                                 |
| USSector         | US Industry Board                             |
| USOption         | US Option                                     |
| USOptionS        | US Sepecial Option (market closed at 4:15 pm) |
| HKEquity         | Hong Kong Equity Securities                   |
| HKPreIPO         | HK PreIPO Security                            |
| HKWarrant        | HK Warrant                                    |
| HKHS             | Hang Seng Index                               |
| HKSector         | HK Industry Board                             |
| SHMainConnect    | SH Main Board(Connect)                        |
| SHMainNonConnect | SH Main Board(Non Connect)                    |
| SHSTAR           | SH Science and Technology Innovation Board    |
| CNIX             | CN Index                                      |
| CNSector         | CN Industry Board                             |
| SZMainConnect    | SZ Main Board(Connect)                        |
| SZMainNonConnect | SZ Main Board(Non Connect)                    |
| SZGEMConnect     | SZ Gem Board(Connect)                         |
| SZGEMNonConnect  | SZ Gem Board(Non Connect)                     |
| SGMain           | SG Main Board                                 |
| STI              | Singapore Straits Index                       |
| SGSector         | SG Industry Board                             |
