name: Deploy Canary
on:
  push:
    branches:
      - main
      - canary

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  deploy_canary:
    name: build
    timeout-minutes: 30
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Install ossutil
        run: |
          wget https://github.com/aliyun/ossutil/releases/download/v1.7.17/ossutil-v1.7.17-linux-amd64.zip
          unzip ossutil-v1.7.17-linux-amd64.zip && cp ./ossutil-v1.7.17-linux-amd64/ossutil ${{github.workspace}}/ && chmod +x ${{github.workspace}}/ossutil

      - name: Test ossutil
        run: |
          ${{github.workspace}}/ossutil --version

      - uses: oven-sh/setup-bun@v2

      - name: Install dependency
        run: bun install

      - name: Build canary
        run: bun run build:canary

      - name: Upload to <PERSON>yun OSS
        run: |
          ${{github.workspace}}/ossutil cp ./docs/.vitepress/dist oss://lb-assets/github/canary/open.longportapp.com/new-docs/raw/ -u -r -j 10 -e oss-cn-hangzhou.aliyuncs.com -i ${{ secrets.FE_LB_ASSET_ACCESS_KEY_ID }} -k ${{ secrets.FE_LB_ASSET_ACCESS_KEY_SECRET }} --exclude "*.html" --exclude "*.md" --meta=Cache-Control:max-age=31536000
          ${{github.workspace}}/ossutil cp ./docs/.vitepress/dist oss://lb-assets/github/canary/open.longportapp.com/new-docs/raw/ -u -r -j 10 -e oss-cn-hangzhou.aliyuncs.com -i ${{ secrets.FE_LB_ASSET_ACCESS_KEY_ID }} -k ${{ secrets.FE_LB_ASSET_ACCESS_KEY_SECRET }} --include "*.md" --meta "Cache-Control:no-cache#Content-type:text/markdown;charset=utf-8"
          ${{github.workspace}}/ossutil cp ./docs/.vitepress/dist oss://lb-assets/github/canary/open.longportapp.com/new-docs/raw/ -u -r -j 10 -e oss-cn-hangzhou.aliyuncs.com -i ${{ secrets.FE_LB_ASSET_ACCESS_KEY_ID }} -k ${{ secrets.FE_LB_ASSET_ACCESS_KEY_SECRET }} --include "*.html" --meta "Cache-Control:no-cache#Content-type:text/html;charset=utf-8"
