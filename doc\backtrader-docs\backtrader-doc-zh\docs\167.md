# 向 OHLC 索取投标资料

> 原文： [https://www.backtrader.com/blog/posts/2016-04-14-bidask-data-to-ohlc/bidask-data-to-ohlc/](https://www.backtrader.com/blog/posts/2016-04-14-bidask-data-to-ohlc/bidask-data-to-ohlc/)

最近，backtrader 通过实现行重写执行了一次从 ohlc land 的逃逸，这允许重新定义整个层次结构，例如，具有只包含 bid、ask 和 datetime 行的数据提要。

（此处为 OHLC 陆地的原始逃生）

这就引出了如何可视化这些数据的问题，这是以`OHLC`格式（无论是`bar`还是`candlestick`格式）最有效地实现的

需要采取的步骤：

1.  定义一个可以读取给定`bid/ask`格式的数据馈送加载器

2.  决定将值分配给哪些字段，即：`open`、`high`、`low`和`close`（可能还有`volume`

3.  决定重采样方案

源数据（10 行买卖数据）：

```py
Date,Time,Symbol,Status,Bid,Ask,Bid Vol,Ask Vol
01/03/16,23:43:11,EUR/JPY,D,,130.520,,1000000
01/03/16,23:43:27,EUR/JPY,D,,130.520,,2000000
01/03/16,23:49:19,EUR/JPY,D,,130.510,,500000
01/03/16,23:49:22,EUR/JPY,D,,130.530,,1500000
01/03/16,23:49:25,EUR/JPY,D,,130.540,,750000
01/03/16,23:49:27,EUR/JPY,D,,130.550,,900000
01/03/16,23:51:25,EUR/JPY,D,,130.500,,1200000
01/03/16,23:52:27,EUR/JPY,D,,130.495,,1100000
01/03/16,23:53:25,EUR/JPY,D,,130.480,,600000
01/03/16,23:54:27,EUR/JPY,D,,130.470,,900000 
```

之后：

*   读取数据不会是一个大问题，因为最终结果必须是 OHLC，而这就是内置数据提要在解析后提供的内容。因为它是*csv*的另一个变体。我们甚至可以重用`GenericCSVData`现有的提要。感谢上帝，它是*通用*

*   每行只有一个*价格*元素和一个*成交量*元素，价格分配很明确：*将价格分配给四个价格元素，将成交量分配给成交量*

*   当涉及到重采样时，而不是向上采样到更大的时间范围，关键是条数，即：*压缩*

    而内置的*重采样器*已经可以提供相同的*时间段*但经过压缩

通过`GenericCSVData`将数据转换成 OHLC 格式：

```py
 data = btfeeds.GenericCSVData(
        dataname=args.data,
        dtformat='%d/%m/%y',
        # tmformat='%H%M%S',  # already the default value
        # datetime=0,  # position at default
        time=1,  # position of time
        open=5,  # position of open
        high=5,
        low=5,
        close=5,
        volume=7,
        openinterest=-1,  # -1 for not present
        timeframe=bt.TimeFrame.Ticks) 
```

有些参数甚至不需要更改，即：

*   `tmformat`：因为提要中的时间已经与默认格式匹配

*   `datetime`：因为日期在 csv 流的第一个位置

其他人：

*   `time=1`：表示时间与`date`不在一个字段中，且不在该字段的哪个位置

*   `open=5`（与`high`、`low`、`close`相同）：流中哪个字段将作为价格来源

*   `volume=7`：同上

*   `openinterest=-1`：负值表示该字段不存在

一旦数据在板上，就只需对其重新采样：

```py
 cerebro.resampledata(data,
                         timeframe=bt.TimeFrame.Ticks,
                         compression=args.compression) 
```

我们提供与数据携带的`TimeFrame.Ticks`相同的`timeframe`，以确保数据没有*上采样*。`compression`作为来自命令行的参数，因此：`compression=args.compression`

执行示例：

```py
$ ./bidask-to-ohlc.py --compression 2
2016-03-01 23:43:27,130.52,130.52,130.52,130.52,3000000.0
2016-03-01 23:49:22,130.51,130.53,130.53,130.53,2000000.0
2016-03-01 23:49:27,130.54,130.55,130.55,130.55,1650000.0
2016-03-01 23:52:27,130.5,130.5,130.5,130.495,2300000.0
2016-03-01 23:54:27,130.48,130.48,130.48,130.47,1500000.0 
```

不出所料，由于`2`被分配到压缩，我们已经从*出价/询问*格式变为*OHLC*格式，从`10`变为`5`行数据。

`backtrader`不能创造奇迹也就不足为奇了，使用一个不是原始行总数除数的*压缩*因子，它将产生`rows / compression + 1`新行：

```py
$ ./bidask-to-ohlc.py --compression 3
2016-03-01 23:49:19,130.52,130.52,130.52,130.51,3500000.0
2016-03-01 23:49:27,130.53,130.55,130.55,130.55,3150000.0
2016-03-01 23:53:25,130.5,130.5,130.5,130.48,2900000.0
2016-03-01 23:54:27,130.47,130.47,130.47,130.47,900000.0 
```

在本例中为`10 / 3 = 3.33333`，这就是`4`行被传递的原因。

当然，现在手上拿着`OHLC`就可以绘制结果了。由于数据量少、数据方差低，以及`matplotlib`内部如何处理这种情况，因此图表并不十分好看。

[![!image](img/85c14ac5921762847465ca04dac151fd.png)](../bidask-data-to-ohlc.png)

样本代码（包含在`backtrader`的来源中）

```py
from __future__ import (absolute_import, division, print_function,)
#                        unicode_literals)

import argparse
import datetime

import backtrader as bt
import backtrader.feeds as btfeeds

class St(bt.Strategy):
    def next(self):
        print(','.join(str(x) for x in [
            self.data.datetime.datetime(),
            self.data.open[0], self.data.high[0],
            self.data.high[0], self.data.close[0],
            self.data.volume[0]]))

def runstrat():
    args = parse_args()

    cerebro = bt.Cerebro()

    data = btfeeds.GenericCSVData(
        dataname=args.data,
        dtformat='%d/%m/%y',
        # tmformat='%H%M%S',  # already the default value
        # datetime=0,  # position at default
        time=1,  # position of time
        open=5,  # position of open
        high=5,
        low=5,
        close=5,
        volume=7,
        openinterest=-1,  # -1 for not present
        timeframe=bt.TimeFrame.Ticks)

    cerebro.resampledata(data,
                         timeframe=bt.TimeFrame.Ticks,
                         compression=args.compression)

    cerebro.addstrategy(St)

    cerebro.run()
    if args.plot:
        cerebro.plot(style='bar')

def parse_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description='BidAsk to OHLC')

    parser.add_argument('--data', required=False,
                        default='../../datas/bidask2.csv',
                        help='Data file to be read in')

    parser.add_argument('--compression', required=False, default=2, type=int,
                        help='How much to compress the bars')

    parser.add_argument('--plot', required=False, action='store_true',
                        help='Plot the vars')

    return parser.parse_args()

if __name__ == '__main__':
    runstrat() 
```