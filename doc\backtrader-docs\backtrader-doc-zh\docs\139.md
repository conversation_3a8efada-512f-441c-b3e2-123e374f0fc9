# Python 的隐藏力量（2）

> 原文： [https://www.backtrader.com/blog/posts/2016-11-23-hidden-powers-2/hidden-powers/](https://www.backtrader.com/blog/posts/2016-11-23-hidden-powers-2/hidden-powers/)

让我们进一步探讨一下 Python 的*隐藏功能*是如何在*backtrader*中使用的，以及如何实现这一功能以实现主要目标：*易用性*

## 这些定义是什么？

例如，指标：

```py
import backtrader as bt

class MyIndicator(bt.Indicator):

    lines = ('myline',)
    params = (('period', 20),)

    ... 
```

任何能够阅读 python 的人都会说：

*   `lines`是一个`tuple`，实际上包含一个项目，一个字符串

*   `params`也是一个`tuple`，包含另一个`tuple`带 2 项

## 但后来

扩展示例：

```py
import backtrader as bt

class MyIndicator(bt.Indicator):

    lines = ('myline',)
    params = (('period', 20),)

    def __init__(self):

        self.lines.myline = (self.data.high - self.data.low) / self.p.period 
```

在这里，任何人都应该明白：

*   *类*中`lines`的定义已转化为一个属性，该属性可作为`self.lines`访问，并依次包含定义中指定的属性`myline`

和

*   *类*中`params`的定义已经转化为一个属性，该属性可以达到`self.p`（或`self.params`，并依次包含定义中指定的属性`period`

    而且`self.p.period`似乎有一个值，因为它直接用于算术运算（显然该值是定义中的值：`20`）

## 答案是：元类

`bt.Indicator`因此`MyIndicator`也有一个*元类*，这允许应用*元编程*概念。

在这种情况下，*截取``行``和``参数``的定义，使其成为*：

*   *实例*的属性，即：可达为`self.lines`和`self.params`

*   *类*的属性

*   包含其中定义的`atributes`（和定义值）

## 部分秘密

对于那些不精通*元类*的人来说，或多或少是这样做的：

```py
class MyMetaClass(type):

    def __new__(meta, name, bases, dct):
        ...

        lines = dct.pop('lines', ())
        params = dct.pop('params', ())

        # Some processing of lines and params ... takes place here

        ...

        dct['lines'] = MyLinesClass(info_from_lines)
        dct['params'] = MyParamsClass(info_from_params)

        ... 
```

这里拦截了*类*的创建，并根据从定义中提取的信息，将`lines`和`params`的定义替换为*类*。

这一点本身无法达到，因此实例的创建也被拦截。使用 Pyton 3.x 语法：

```py
class MyClass(Parent, metaclass=MyMetaClass):

    def __new__(cls, *args, **kwargs):

        obj = super(MyClass, cls).__new__(cls, *args, **kwargs)
        obj.lines = cls.lines()
        obj.params = cls.params()

        return obj 
```

这里，在*实例中，上面定义为`MyLinesClass`和`MyParamsClass`的*实例被放入`MyClass`实例中。

不，没有冲突：

*   *类*的意思是：“系统范围”，并且包含`lines`和`params`的自身属性，这两个类都是类

*   *实例*的意思是：“系统本地”，每个实例都包含`lines`和`params`的实例（每次都不同）

例如，通常可以使用`self.lines`访问实例，但也可以使用`MyClass.lines`访问类。

后者为用户提供了对方法的访问权限，这些方法并不适用于一般用途，但这是 Python，任何东西都不能被禁止，更不用说使用*开源*

## 结论

元类在幕后工作，提供一种机制，通过处理诸如`lines`和`params`的`tuple`定义之类的东西，使 almos 成为一种元语言

目标是让任何使用平台的人的生活更轻松