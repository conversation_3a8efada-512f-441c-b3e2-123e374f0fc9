# 作家

> 原文： [https://www.backtrader.com/docu/writer/](https://www.backtrader.com/docu/writer/)

将以下内容写入流：

*   带有数据源、策略、指标和观察者的 csv 流

    通过每个对象的`csv`属性可以控制哪些对象实际进入 csv 流（对于`data feeds`默认为 True，对于`indicators`默认为`observers`/False）

*   网络特性综述

    *   数据源

    *   策略（线路和参数）

    *   指标/观察员：（线路和参数）

    *   分析仪：（参数和分析结果）

仅定义了一个名为`WriterFile`的编写器，可以添加到系统中：

*   通过将大脑的`writer`参数设置为 True

    标准`WriterFile`将被实例化

*   通过呼叫`Cerebro.addwriter(writerclass, **kwargs)`

    `writerclass`将在使用 givenn`kwargs`执行回溯测试时实例化

    鉴于标准`WriterFile`没有将`csv`作为默认输出，下面的`addwriter`调用将处理它：

    ```py
    cerebro.addwriter(bt.WriterFile, csv=True) 
    ```

## 参考

#### 类 backtrader.WriterFile（）

系统范围的编写器类。

可使用以下参数对其进行参数化：

*   `out`（默认值：`sys.stdout`：要写入的输出流

    如果传递字符串，则将使用包含参数内容的文件名

*   `close_out`（默认为`False`）

    如果`out`是一个流，则写入程序是否必须显式关闭它

*   `csv`（默认为`False`）

    若在执行过程中必须将数据源、策略、观察者和指标的 csv 流写入该流

    通过每个对象的`csv`属性可以控制哪些对象实际进入 csv 流（对于`data feeds`默认为`True`，对于`indicators`默认为`observers`/False）

*   `csv_filternan`（默认值：`True`）是否必须从 csv 流中清除`nan`值（替换为空字段）

*   `csv_counter`（默认值：`True`）如果编写器需要保留并打印出实际输出行的计数器

*   `indent`（默认值：`2`）每个级别的缩进空间

*   `separators`（默认为`['=', '-', '+', '*', '.', '~', '"', '^', '#']`）

    用于跨节/子（子）节的行分隔符的字符

*   `seplen`（默认为`79`）

    行分隔符的总长度，包括缩进

*   `rounding`（默认为`None`）

    要舍入的小数位数向下浮动。使用`None`时，不进行舍入