# Backtrader 中文文档

> 原文：[Backtrader Document](https://www.backtrader.com/)
> 
> 协议：[CC BY-NC-SA 4.0](http://creativecommons.org/licenses/by-nc-sa/4.0/)
> 
> 阶段：机翻（1）
> 
> 自豪地采用[谷歌翻译](https://translate.google.cn/)
> 
> 欢迎任何人参与和完善：一个人可以走的很快，但是一群人却可以走的更远。

* [在线阅读](https://backtrader.apachecn.org)
* [在线阅读（Gitee）](https://apachecn.gitee.io/backtrader-doc-zh/)
* [ApacheCN 学习资源](http://docs.apachecn.org/)

## 贡献指南

本项目需要校对，欢迎大家提交 Pull Request。

> 请您勇敢地去翻译和改进翻译。虽然我们追求卓越，但我们并不要求您做到十全十美，因此请不要担心因为翻译上犯错——在大部分情况下，我们的服务器已经记录所有的翻译，因此您不必担心会因为您的失误遭到无法挽回的破坏。（改编自维基百科）

## 联系方式

### 负责人

* [片刻](https://github.com/jiangzhonglian): 529815144

### 其他

*   在我们的 [apachecn/backtrader-doc-zh](https://github.com/apachecn/backtrader-doc-zh) github 上提 issue.
*   发邮件到 Email: `<EMAIL>`.
*   在我们的 [组织学习交流群](http://www.apachecn.org/organization/348.html) 中联系群主/管理员即可.

## 下载

### Docker

```
docker pull apachecn0/backtrader-doc-zh
docker run -tid -p <port>:80 apachecn0/backtrader-doc-zh
# 访问 http://localhost:{port} 查看文档
```

### PYPI

```
pip install backtrader-doc-zh
backtrader-doc-zh <port>
# 访问 http://localhost:{port} 查看文档
```

### NPM

```
npm install -g backtrader-doc-zh
backtrader-doc-zh <port>
# 访问 http://localhost:{port} 查看文档
```

## 赞助我们

![](http://data.apachecn.org/img/about/donate.jpg)
