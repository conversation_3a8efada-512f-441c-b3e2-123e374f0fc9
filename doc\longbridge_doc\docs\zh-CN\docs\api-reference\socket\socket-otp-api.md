---
title: 获取长连接 OTP
id: socket-otp-api
slug: /socket-token-api
sidebar_position: 2
---

# 获取长连接 OTP(One Time Password)

获取长连接使用的 `Token`(One time password)，长连接的 `Token` 可以用来连接行情和交易的长连接网关，是一次性的，使用过后就会作废。

> 最后更新于 2022-04-28

## 请求

| 基本信息    |                  |
| ----------- | ---------------- |
| HTTP URL    | /v1/socket/token |
| HTTP Method | GET              |

### 请求头

| 名称          | 类型   | 必须 | 描述                                          |
| ------------- | ------ | ---- | --------------------------------------------- |
| Authorization | string | 是   |                                               |
| Content-Type  | string | 是   | **固定值**："application/json; charset=utf-8" |

### 请求参数

## 响应

### 响应体

| 名称    | 类型   | 描述                  |
| ------- | ------ | --------------------- |
| code    | int    | 错误码，非 0 表示失败 |
| msg     | string | 错误描述              |
| data    | object |                       |
| ∟otp    | string | 获取到的 token        |
| ∟limit  | int    | 连接限制总数          |
| ∟online | int    | 当前在线连接数        |

### 响应体示例

```json
{
  "code": 0,
  "message": "",
  "data": {
    "otp": "xxxxxxxx",
    "limit": 10,
    "online": 3
  }
}
```
