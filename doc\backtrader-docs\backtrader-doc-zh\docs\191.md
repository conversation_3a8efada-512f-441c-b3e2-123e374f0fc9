# 通用 CSV 数据源

> 原文： [https://www.backtrader.com/blog/posts/2015-08-04-generic-csv-datafeed/generic-csv-datafeed/](https://www.backtrader.com/blog/posts/2015-08-04-generic-csv-datafeed/generic-csv-datafeed/)

一个问题导致了**GenericCSVData**的实现，该数据可用于解析不同的 CSV 格式。

GitHub 中的问题[问题](https://github.com/mementum/backtrader/issues/6)清楚地表明，需要有能够实际处理任何传入 CSV 数据提要的东西。

酱汁在 params 声明中：

```py
class GenericCSVData(feed.CSVDataBase):
    params = (
        ('nullvalue', float('NaN')),
        ('dtformat', '%Y-%m-%d %H:%M:%S'),
        ('tmformat', '%H:%M:%S'),

        ('datetime', 0),
        ('time', -1),
        ('open', 1),
        ('high', 2),
        ('low', 3),
        ('close', 4),
        ('volume', 5),
        ('openinterest', 6),
    ) 
```

由于该类继承自 CSVDataBase，因此可以使用一些标准参数：

*   `fromdate`（使用 datetime 对象限制开始日期）

*   `todate`（带 datetime 对象）限制结束日期）

*   `headers`（默认为 True，表示 CSV 数据是否有标题行）

*   `separator`（默认值：，，分隔字段的字符）

*   `dataname`（包含 CSV 数据或类似文件对象的文件的名称）

其他一些参数，如`name`、`compression`和`timeframe`只是提供信息，除非您计划执行重采样。

当然，更重要的是，新定义参数的含义：

*   包含日期（或日期时间）字段的`datetime`（默认值：0）列

*   `time`（默认值：-1）如果与日期时间字段分开，则包含时间字段的列（-1 表示不存在）

*   `open`（默认值：1）、`high`（默认值：2）、`low`（默认值：3）、`close`（默认值：4）、`volume`（默认值：5）、`openinterest`（默认值：6）

    包含相应字段的列的索引

    如果传递负值（例如：-1），则表示该字段不存在于 CSV 数据中

*   `nullvalue`（默认值：浮点（'NaN'））

    如果缺少应存在的值（CSV 字段为空），将使用的值

*   `dtformat`（默认值：%Y-%m-%d%H:%m:%S）

    用于分析 datetime CSV 字段的格式

*   `tmformat`（默认值：%H:%M:%S）

    用于在“存在”时解析时间 CSV 字段的格式（“时间”CSV 字段的默认值不存在）

这可能足以涵盖许多不同的 CSV 格式和缺少值的情况。

包含以下要求的示例用法：

*   将投入限制在 2000 年

*   HLOC 订单而非 OHLC

*   缺少要替换为零（0.0）的值

*   提供每日条形图，日期时间仅为 YYYY-MM-DD 格式的日期

*   不存在`openinterest`列

守则：

```py
import datetime
import backtrader as bt
import backtrader.feeds as btfeed

...
...

data = btfeed.GenericCSVData(
    dataname='mydata.csv',

    fromdate=datetime.datetime(2000, 1, 1),
    todate=datetime.datetime(2000, 12, 31),

    nullvalue=0.0,

    dtformat=('%Y-%m-%d'),

    datetime=0,
    high=1,
    low=2,
    open=3,
    close=4,
    volume=5,
    openinterest=-1
) 
```

稍作修改的要求：

*   将投入限制在 2000 年

*   HLOC 订单而非 OHLC

*   缺少要替换为零（0.0）的值

*   提供日内条形图，带有单独的日期和时间列

    *   日期的格式为 YYYY-MM-DD
    *   时间的格式为 HH.MM.SS
*   不存在`openinterest`列

守则：

```py
import datetime
import backtrader as bt
import backtrader.feeds as btfeed

...
...

data = btfeed.GenericCSVData(
    dataname='mydata.csv',

    fromdate=datetime.datetime(2000, 1, 1),
    todate=datetime.datetime(2000, 12, 31),

    nullvalue=0.0,

    dtformat=('%Y-%m-%d'),
    tmformat=('%H.%M.%S'),

    datetime=0,
    time=1,
    high=2,
    low=3,
    open=4,
    close=5,
    volume=6,
    openinterest=-1
) 
```