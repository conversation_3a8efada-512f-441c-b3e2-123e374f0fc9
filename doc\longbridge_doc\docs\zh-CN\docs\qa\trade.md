---
title: 交易相关
sidebar_position: 2
---

## Q1: 支持哪些订单类型？

A: 模拟账户和真实账户均支持普通限价单、市价单及到价买入、到价卖出等条件订单，暂不支持附加订单及网格订单。

## Q2: 模拟账户交易时间有哪些？

A: 模拟账户港股时间与真实环境相同，美股暂未支持盘前盘后交易，仅支持常规交易时段模拟撮合。

## Q3: 如何进美股夜盘交易？

A: 委托下单接口通过 `outside_rth` 参数传递 [OVERNIGHT](/docs/trade/order/submit#parameters) 枚举即为指定夜盘交易的订单。

## Q4: 模拟账户下的交易规则？

A: 模拟账户目前支持港美股股票、ETF、港股轮证交易，其中美股支持股票做空。美股 OTC、盘前盘后交易、期权交易在模拟账号下暂未支持。

模拟交易参考真实市场买卖摆盘进行撮合，即当委托买入价高于等于摆盘卖一价、委托卖出价低于等于摆盘买一价，即可撮合成交，市价单默认均可成交。

## Q5: 如何重置模拟账户资金？

A: 暂不支持用户手动重置模拟资金，如有需求可联系您的客服或客户经理，进行线下处理。

## Q6: 通过 OpenAPI 下单后，我如何看到订单？

A: 经 OpenAPI 委托订单后，可通过调用订单查询接口，查询订单实时状态，或通过对接 WebSocket 推送接收订单更新信息，也可以通过 App/PC 等终端产品直接查看对应账户下的订单及其状态。

## Q7: 如何知道账户资金是否足够交易？

A: 可调用交易类接口 `/v1/trade/estimate/buy_limit` 获取账户现金可买、融资可买及可卖空的数量，因系统风控要求逻辑复杂，不建议自己计算可交易数量。

## Q8: 下单接口返回“用户认证失败”什么意思？

A: 通常是对应交易业务的权限暂未开通，例如期权、美股卖空等。可通过 App 委托挂单引导的权限开通流程，完成权限开通后，继续通过 OpenAPI 完成交易或其他操作。
