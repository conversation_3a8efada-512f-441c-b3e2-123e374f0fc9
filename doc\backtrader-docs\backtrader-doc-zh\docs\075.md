# 分析器参考

> 原文： [https://www.backtrader.com/docu/analyzers-reference/](https://www.backtrader.com/docu/analyzers-reference/)

## 年收益率

#### 类 backtrader.analyzers.AnnualReturn（）

该分析器通过查看年初和年末来计算年度回报

参数：

*   （无）

成员属性：

*   `rets`：计算年度收益表

*   `ret`：年报字典（关键字：年份）

**获取 _ 分析**：

*   返回年度报表字典（关键字：年）

## 卡尔玛

#### 类 backtrader.analyzers.Calmar（）

该分析仪计算的 CalmarRatio 时间范围可能不同于基础数据参数中使用的时间范围：

*   `timeframe`（默认值：`None`）如果`None`使用系统中 1<sup>st</sup>数据的`timeframe`

    通过 PoT T0 来考虑没有时间约束的整个数据集

*   `compression`（默认为`None`）

    仅用于子天时间范围，例如，通过指定“timeframe.Minutes”和 60 作为压缩，在每小时时间范围内工作

    如果为`None`，则将使用系统的 1<sup>st</sup>数据压缩

*   *无*

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

#### -“`get_analysis``（）

返回一个 OrderedDict，其中包含该时间段的键和相应的滚动 Calmar 比率

#### -“`calmar``最新计算的 calmar 比率（）

## 缩编

#### 类 backtrader.analyzers.DrawDown（）

此分析器计算交易系统提款统计信息，例如以%s 和美元表示的提款值、%s 和美元表示的最大提款、提款长度和提款最大长度

参数：

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

#### -“`get_analysis``（）

返回一个字典（支持.notation 和 subdctionaries），其中 drawdown stats 作为值，以下键/属性可用：

*   `drawdown`-0.xx%的提取值

*   `moneydown`-以货币单位表示的支取价值

*   `len`-水位下降长度

*   `max.drawdown`-0.xx%的最大下降值

*   `max.moneydown`-以货币单位表示的最大提取价值

*   `max.len`-最大水位下降长度

## 时间下降

#### 类 backtrader.analyzers.TimeDrawDown（）

此分析器计算所选时间范围内的交易系统提款，该时间范围可能不同于基础数据参数中使用的时间范围：

*   `timeframe`（默认值：`None`）如果`None`使用系统中 1<sup>st</sup>数据的`timeframe`

    通过 PoT T0 来考虑没有时间约束的整个数据集

*   `compression`（默认为`None`）

    仅用于子天时间范围，例如，通过指定“timeframe.Minutes”和 60 作为压缩，在每小时时间范围内工作

    如果为`None`，则将使用系统的 1<sup>st</sup>数据压缩

*   *无*

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

#### -“`get_analysis``（）

返回一个字典（支持.notation 和 subdctionaries），其中 drawdown stats 作为值，以下键/属性可用：

*   `drawdown`-0.xx%的提取值

*   `maxdrawdown`-以货币单位表示的支取价值

*   `maxdrawdownperiod`-水位下降长度

#### -这些在运行期间作为属性（）可用

*   `dd`

*   `maxdd`

*   `maxddlen`

## 格罗斯平均

#### 类 backtrader.analyzers.GrossLeverage（）

此分析器根据时间框架计算当前战略的总杠杆率

参数：

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

#### -get_ 分析（）

返回一个字典，其中返回值为值，每次返回的日期时间点为键

## 位置值

#### 类 backtrader.analyzers.PositionsValue（）

此分析器报告当前数据集的位置值

参数：

*   时间段（默认值：`None`），如果为`None`，则使用系统的 1<sup>st</sup>数据的时间段

*   压缩（默认值：`None`

    仅用于子天时间范围，例如，通过指定“timeframe.Minutes”和 60 作为压缩，在每小时时间范围内工作

    如果为`None`，则将使用系统的 1<sup>st</sup>数据压缩

*   标题（默认值：`False`）

    将初始键添加到包含结果的字典中，并将数据名（'Datetime'作为键）

*   现金（默认值：`False`

    包括实际现金作为额外头寸（标题“现金”将用作名称）

#### -get_ 分析（）

返回一个字典，其中返回值为值，每次返回的日期时间点为键

## 文件夹

#### 类 backtrader.analyzers.PyFolio（）

该分析仪使用 4 个子分析仪收集数据，并将其转换为与`pyfolio`兼容的数据集

儿童分析仪

*   `TimeReturn`

    用于计算全球投资组合价值的回报

*   `PositionsValue`

    用于计算每个数据的位置值。将`headers`和`cash`参数设置为`True`

*   `Transactions`

    用于在数据（大小、价格、价值）上记录每笔交易。将`headers`参数设置为`True`

*   `GrossLeverage`

    跟踪总杠杆率（战略投资额）

参数：

```py
These are passed transparently to the children

* timeframe (default: `bt.TimeFrame.Days`)

  If `None` then the timeframe of the 1st data of the system will be
  used

* compression (default: 1\`)

  If `None` then the compression of the 1st data of the system will be
  used 
```

`timeframe`和`compression`都是按照`pyfolio`的默认行为设置的，该行为处理*每日*数据，并对其进行上采样以获得年度回报等值。

#### -get_ 分析（）

返回一个字典，其中返回值为值，每次返回的日期时间点为键

#### 获取\u pf\u 项目（）

返回由 4 个元素组成的元组，这些元素可用于进一步处理

```py
`pyfolio`

returns, positions, transactions, gross_leverage 
```

因为对象是用来直接输入到`pyfolio`的，所以该方法进行`pandas`的本地导入，将内部*backtrader*结果转换为*pandas 数据帧*，这是`pyfolio.create_full_tear_sheet`等的预期输入

如果未安装`pandas`，则该方法将中断

## 日志返回滚动

#### 类 backtrader.analyzers.LogReturnsRolling（）

此分析器计算给定时间段和压缩的滚动回报

参数：

*   `timeframe`（默认值：`None`）如果`None`使用系统中 1<sup>st</sup>数据的`timeframe`

    通过 PoT T0 来考虑没有时间约束的整个数据集

*   `compression`（默认为`None`）

    仅用于子天时间范围，例如，通过指定“timeframe.Minutes”和 60 作为压缩，在每小时时间范围内工作

    如果为`None`，则将使用系统的 1<sup>st</sup>数据压缩

*   `data`（默认为`None`）

    要跟踪的参考资产，而不是投资组合价值。

    **注**：此数据必须已添加到`addata`、`resampledata`或`replaydata`的`cerebro`实例中

*   `firstopen`（默认为`True`）

    跟踪`data`的返回时，在跨越时间范围边界时，例如`Years`时，执行以下操作：

    *   上一年度的最后一个`close`作为参考价格，以查看本年度的收益

    问题是 1<sup>st</sup>计算，因为数据没有**之前的**收盘价。因此，当该参数为`True`时，1<sup>st</sup>计算将使用*期初*价格。

    这要求数据馈送具有`open`价格（对于`close`将使用标准[0]符号，而不参考字段价格）

    否则将使用初始关闭。

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

#### -get_ 分析（）

返回一个字典，其中返回值为值，每次返回的日期时间点为键

## 周期统计

#### 类 backtrader.analyzers.PeriodStats（）

计算给定时间段的基本统计信息

参数：

*   `timeframe`（默认值：`Years`）如果`None`使用系统中 1<sup>st</sup>数据的`timeframe`

    通过 PoT T0 来考虑没有时间约束的整个数据集

*   `compression`（默认为`1`）

    仅用于子天时间范围，例如，通过指定“timeframe.Minutes”和 60 作为压缩，在每小时时间范围内工作

    如果为`None`，则将使用系统的 1<sup>st</sup>数据压缩

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

`get_analysis`返回包含键的字典：

*   `average`

*   `stddev`

*   `positive`

*   `negative`

*   `nochange`

*   `best`

*   `worst`

如果参数`zeroispos`设置为`True`，则没有变化的周期将被计为正

## 退换商品

#### 类 backtrader.analyzers.Returns（）

使用对数方法计算的总回报、平均回报、复合回报和年化回报

见：

*   [https://www.crystalbull.com/sharpe-ratio-better-with-log-returns/](https://www.crystalbull.com/sharpe-ratio-better-with-log-returns/)

参数：

*   `timeframe`（默认为`None`）

    如果使用系统中 1<sup>st</sup>数据的`None`号`timeframe`号

    通过 PoT T0 来考虑没有时间约束的整个数据集

*   `compression`（默认为`None`）

    仅用于子天时间范围，例如，通过指定“timeframe.Minutes”和 60 作为压缩，在每小时时间范围内工作

    如果为`None`，则将使用系统的 1<sup>st</sup>数据压缩

*   `tann`（默认为`None`）

    用于项目年度化（标准化）的期间数

    即：

    *   `days: 252`

    *   `weeks: 52`

    *   `months: 12`

    *   `years: 1`

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

#### -get_ 分析（）

返回一个字典，其中返回值为值，每次返回的日期时间点为键

返回的 dict 包含以下键：

*   `rtot`：复合收益总额

*   `ravg`：整个期间的平均回报率（特定时间段）

*   `rnorm`：年化/标准化回报

*   `rnorm100`：以 100%表示的年化/标准化回报率

## 夏佩拉蒂奥

#### 类 backtrader.analyzers.SharpeRatio（）

该分析仪使用无风险资产（即简单的利率）计算策略的收益率

参数：

*   `timeframe`：（默认值：`TimeFrame.Years`

*   `compression`（默认为`1`）

    仅用于子天时间范围，例如，通过指定“timeframe.Minutes”和 60 作为压缩，在每小时时间范围内工作

*   `riskfreerate`（默认值：0.01->1%）

    以年度条款表示（见下文`convertrate`）

*   `convertrate`（默认为`True`）

    将`riskfreerate`从年费率转换为月费率、周费率或日费率。不支持分日转换

*   `factor`（默认为`None`）

    如果`None`，则无风险利率从*年*到所选时间段的转换系数将从预定义表格中选择

    天数：252 周：52 月：12 年：1

    否则将使用指定的值

*   `annualize`（默认为`False`）

    如果`convertrate`为`True`，则*SharpeRatio*将在选择的`timeframe`中交付。

    在大多数情况下，夏普拉蒂奥以年度形式交付。将`riskfreerate`从年费率转换为月费率、周费率或日费率。不支持分日转换

*   `stddev_sample`（默认为`False`）

    如果设置为`True`，将计算*标准偏差*，将平均值中的分母减少`1`。如果认为并非所有样本都用于计算，则在计算*标准偏差*时使用。这被称为*贝塞尔校正*

*   `daysfactor`（默认为`None`）

    `factor`的旧名称。如果设置为除`None`以外的任何值，且`timeframe`为`TimeFrame.Days`，则将假定这是旧代码，并将使用该值

*   `legacyannual`（默认为`False`）

    使用`AnnualReturn`返回分析器，顾名思义，它只在年份上工作

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

#### -get_ 分析（）

返回一个字典，其中键“sharperatio”保持比率

## 夏佩拉蒂奥

#### 类 backtrader.analyzers.SharpeRatio_A（）

夏普比率的扩展，它以年化形式直接返回夏普比率

以下参数已从`SharpeRatio`更改

*   `annualize`（默认为`True`）

## SQN

#### 类 backtrader.analyzers.SQN（）

SQN 或系统质量编号。由 Van K.Tharp 定义，用于对交易系统进行分类。

*   低于平均水平 1.6-1.9

*   平均 2.0-2.4

*   2.5-2.9 良好

*   3.0-5.0 优秀

*   5.1-6.9

*   7.0-圣杯？

公式如下：

*   平方根（数字等级）*平均值（交易价格）/STDEV（交易价格）

当交易数量>=30 时，应认为 sqn 值是可靠的

#### -get_ 分析（）

返回键为“sqn”和“trades”（考虑的交易数）的字典

## 报时表

#### 类 backtrader.analyzers.TimeReturn（）

该分析器通过查看时间范围的开始和结束来计算回报

参数：

*   `timeframe`（默认值：`None`）如果`None`使用系统中 1<sup>st</sup>数据的`timeframe`

    通过 PoT T0 来考虑没有时间约束的整个数据集

*   `compression`（默认为`None`）

    仅用于子天时间范围，例如，通过指定“timeframe.Minutes”和 60 作为压缩，在每小时时间范围内工作

    如果为`None`，则将使用系统的 1<sup>st</sup>数据压缩

*   `data`（默认为`None`）

    要跟踪的参考资产，而不是投资组合价值。

    **注**：此数据必须已添加到`addata`、`resampledata`或`replaydata`的`cerebro`实例中

*   `firstopen`（默认为`True`）

    跟踪`data`的返回时，在跨越时间范围边界时，例如`Years`时，执行以下操作：

    *   上一年度的最后一个`close`作为参考价格，以查看本年度的收益

    问题是 1<sup>st</sup>计算，因为数据没有**之前的**收盘价。因此，当该参数为`True`时，1<sup>st</sup>计算将使用*期初*价格。

    这要求数据馈送具有`open`价格（对于`close`将使用标准[0]符号，而不参考字段价格）

    否则将使用初始关闭。

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

#### -get_ 分析（）

返回一个字典，其中返回值为值，每次返回的日期时间点为键

## 贸易分析仪

#### 类 backtrader.analyzers.TradeAnalyzer（）

提供已结束交易的统计信息（同时保留未结束交易的计数）

*   未平仓/已平仓交易总额

*   连胜/失利当前/最长

*   盈亏总额/平均数

*   赢/失计数/总 PNL/平均 PNL/最大 PNL

*   长/短计数/总 PNL/平均 PNL/最大 PNL

    *   赢/失计数/总 PNL/平均 PNL/最大 PNL
*   长度（市场上的钢筋）

    *   总/平均/最大/最小

    *   赢得/损失总额/平均数/最大值/最小值

    *   长/短总/平均/最大/最小

    *   赢得/损失总额/平均数/最大值/最小值

**注**：分析仪对字段使用“自动”dict，这意味着如果没有执行交易，则不会生成统计数据。

在这种情况下，`get_analysis`返回的字典中将有一个字段/子字段，即：

*   dictname['total']['total']的值为 0（该字段也可通过点符号 dictname.total.total 访问）

## 交易

#### 类 backtrader.analyzers.Transactions（）

此分析器报告系统中每个数据发生的事务

它查看订单执行位，以在每个`next`周期中从 0 开始创建一个`Position`。

结果将在下一次记录交易时使用

参数：

*   标题（默认值：`True`）

    将初始键添加到包含数据名称的结果的字典中

    该分析仪建模是为了便于与`pyfolio`集成，标题名称取自用于该分析仪的样本：

    ```py
    'date', 'amount', 'price', 'sid', 'symbol', 'value' 
    ```

#### -get_ 分析（）

返回一个字典，其中返回值为值，每次返回的日期时间点为键

## VWR

#### 类 backtrader.analyzers.VWR（）

可变性加权回报：更好的 SharpeRatio 与日志回报

别名：

*   可变权重回归

见：

*   [https://www.crystalbull.com/sharpe-ratio-better-with-log-returns/](https://www.crystalbull.com/sharpe-ratio-better-with-log-returns/)

参数：

*   `timeframe`（默认值：`None`）如果`None`，则将报告整个回溯测试期间的完整回报

    通过 PoT T0 来考虑没有时间约束的整个数据集

*   `compression`（默认为`None`）

    仅用于子天时间范围，例如，通过指定“timeframe.Minutes”和 60 作为压缩，在每小时时间范围内工作

    如果为`None`，则将使用系统的 1<sup>st</sup>数据压缩

*   `tann`（默认为`None`）

    用于平均回报年度化（标准化）的期间数。如果为`None`，则使用标准`t`值，即：

    *   `days: 252`

    *   `weeks: 52`

    *   `months: 12`

    *   `years: 1`

*   `tau`（默认为`2.0`）

    计算系数（见文献）

*   `sdev_max`（默认为`0.20`）

    最大标准偏差（见文献）

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

#### -get_ 分析（）

返回一个字典，其中返回值为值，每次返回的日期时间点为键

返回的 dict 包含以下键：

*   `vwr`：可变性加权收益