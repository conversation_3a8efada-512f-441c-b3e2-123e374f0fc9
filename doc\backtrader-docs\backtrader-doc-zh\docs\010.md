# 评论/提及

> 原文： [https://www.backtrader.com/home/<USER>/reviews/](https://www.backtrader.com/home/<USER>/reviews/)

### 4-陈

*   [http://boards.4channel.org/biz/thread/7372143/ai-anon-again-here-a-prediction-from-96-hours-ago](http://boards.4channel.org/biz/thread/7372143/ai-anon-again-here-a-prediction-from-96-hours-ago)

 *### 比特币谈话

*   [Re：仅使用 SMA 交叉盘交易，1 日图：无误？](https://bitcointalk.org/index.php?topic=1855541.0)

 *### Bogleheads.org

*   [“钢铁曼宁”持有个股案](https://www.bogleheads.org/forum/viewtopic.php?t=271184&start=50)

 *### 精英阅读器

*   [我从哪里开始？](https://www.elitetrader.com/et/threads/where-do-i-start.310301/)

 *### 获得资本

*   [www.backtrader.com 整合](http://faq.labs.gaincapital.com/topic/1908-wwwbacktradercom-integration/)

 *### 黑客新闻

*   [原油棕榈油期货交易的遗传优化模糊 MA 系统](https://news.ycombinator.com/item?id=18361090)

**   [有人把设计金融工程工具作为爱好吗？](https://news.ycombinator.com/item?id=17088759)* 

 **### 伊恩·莫布斯

*   [反向交易者](http://ianmobbs.com/projects/backtrader)

 *### 中黑

*   [9 大交易工具](https://hackernoon.com/9-great-tools-for-algo-trading-e0938a6856cd)

 *### 中等-斯滕·阿尔弗德

*   [Python 是否适合金融应用程序开发？](https://medium.com/@stenalferd/everybody-is-talking-about-the-growing-popularity-of-python-in-the-industry-these-days-875cfa7fe7d9)

 *### 网络大师

*   [Python 如何在金融和金融科技中使用](https://www.netguru.com/blog/how-python-is-used-in-finance-and-fintech)

 *### PyConUK 2017

*   [https://randomactsofcartography.wordpress.com/2017/11/01/what-i-learned-at-pyconuk-2017/](https://randomactsofcartography.wordpress.com/2017/11/01/what-i-learned-at-pyconuk-2017/)

**   [https://www.slideshare.net/BrianTsang11/pycon-2017-preevent](https://www.slideshare.net/BrianTsang11/pycon-2017-preevent)* 

 **### 奇塔

*   [python 算法交易库](https://qiita.com/neka-nat@github/items/334a567d13f8a0c34ece)

 *### 量子点

*   [量化交易终极母载 Python 包清单](http://quantlabs.net/blog/2016/01/ultimate-motherload-python-packages-list-for-quant-trading/)

 *### 全域论坛

*   [比较各种 python 直播交易平台](https://www.quantopian.com/posts/comparing-various-python-live-trading-platforms)

**   [忘掉它，使用 backtrader！](https://www.quantopian.com/posts/contributors-and-reviewers-needed-for-zipline-live)* 

 **### QuantStart

*   [选择平台进行回溯测试和自动执行](https://www.quantstart.com/articles/Choosing-a-Platform-for-Backtesting-and-Automated-Execution)

 *### 库拉

*   [Algo 交易是如何运作的？个人有没有办法设置它？还有，这背后的技术和逻辑是什么？](https://www.quora.com/How-does-Algo-trading-work-Is-there-any-way-for-an-individual-to-set-it-up-Also-what-is-the-tech-and-logic-behind-this)

**   [用 Python 学习算法交易并测试模型的最佳方法是什么](https://www.quora.com/What-is-the-best-way-to-learn-algorithmic-trading-in-Python-and-test-out-models)

    **   [进入算法交易的好的和坏的原因是什么（作为个人，而不是对冲基金的雇员）？](https://www.quora.com/What-are-good-and-bad-reasons-for-getting-into-algorithmic-trading-as-an-individual-not-an-employee-at-a-hedge-fund)

    **   [对于算法交易，我需要学习什么样的数学？](https://www.quora.com/What-kind-of-mathematics-do-I-need-to-learn-for-algorithmic-trading)

    **   [R/Python 在金融领域有哪些常见的实际用途？](https://www.quora.com/What-are-some-of-the-common-practical-uses-of-R-Python-in-finance)

    **   [随着人工交易的兴起，人工交易会发生什么？](https://www.quora.com/What-would-happen-to-manual-trading-with-the-rise-of-algotrading)

    **   [https://www.quora.com/Which-is-the-best-service-provider-for-algo-trading](https://www.quora.com/Which-is-the-best-service-provider-for-algo-trading)

    **   [python 中支持测试交易策略的最佳库是什么？](https://www.quora.com/Whats-the-best-library-to-back-test-trading-strategies-in-python)

    **   [数据科学/分析对定量/算法交易有用吗？](https://www.quora.com/Is-data-science-analytics-useful-for-quant-algo-trading)

    **   [哪款算法交易软件最好？](https://www.quora.com/Which-is-the-best-algorithm-trading-software)********* 

 ***### 静态图像

*   [Python 中开源反测试框架的现状](http://statsmage.com/backtesting-frameworks-in-python/)

 *### 开发者大会

（葡萄牙语）

*   [Criando robôs para compa e venda de scriptomomedas](http://www.thedevelopersconference.com.br/tdc/2019/belohorizonte/trilha-python#descricao-161)

**   [幻灯片](https://slides.com/rodrigo73/criptomoedas-tdc-2019#/)* 

 **### 托德雷德

*   [博客](https://todotrader.com/blog/)

 *### 头号人物

*   *“中国的启蒙者和开拓者”*

*   [类别：反向交易者](http://www.topquant.vip/?cat=20)

 *### 交易商杂志

*   [加密交易算法——交易密码交易协议](https://www.traders-mag.es/cryptotrading-algoritmico-trading-de-criptodivisas-con-analisis-tecnico/)

 *### X-trader 论坛

*   [Re:Quantopian](https://www.x-trader.net/foro/viewtopic.php?t=18782)***************************