# 公司

> 原文： [https://www.backtrader.com/home/<USER>/companies/](https://www.backtrader.com/home/<USER>/companies/)

列出（或已列出）*反向交易者*或存在公共使用参考的公司名单（*专业简介*、*博客条目*、*文章*…）

笔记

这不是任何形式的背书、工作证明、官方批准（以及任何其他免责声明）

笔记

在“公司”下面，你会发现一些类似于 Un*x 的发行版，它们选择了提供这些发行版

## 公司

### 阿格拉埃菲酒店

*   [https://agoraeaf.com/](https://agoraeaf.com/)

 *来自 LinkedIn 简介和 Rankia/Finanzas.com 文章

### 羊驼

*   [https://pypi.org/project/alpaca-backtrader-api/](https://pypi.org/project/alpaca-backtrader-api/)

 *### α对β

*   [交易波动性对冲权益 https://www.alphaoverbeta.net/trading-volatility-to-hedge-equity/ -](https://www.alphaoverbeta.net/trading-volatility-to-hedge-equity/)

 *### 全部的

*   [https://ch.linkedin.com/in/karl-vernet-599a464/de](https://ch.linkedin.com/in/karl-vernet-599a464/de)

卡尔·韦尔内的部分工作。

### 数据交易

*   [https://datatrading.info/](https://datatrading.info/)

 *有文章的小店，提供算法交易服务。

### 因特里尼奥

*   [https://intrinio.com/](https://intrinio.com/)

 *### 诺盖特数据

*   [https://pypi.org/project/norgatedata/](https://pypi.org/project/norgatedata/)

 *### 量化资本

*   [https://www.quantifycapital.in/](https://www.quantifycapital.in/)

 *### 昆坦斯蒂

*   [https://www.quantinsti.com/](https://www.quantinsti.com/)

 *从博客条目，工作机会。。。

### 风暴经销商有限公司。

*   [https://www.stormdealers.com/](https://www.stormdealers.com/)

 *在网站上列出

### 贸易工厂

*   [https://tradefab.org/](https://tradefab.org/)

 *引用：*“Python Backtrader 脚本的 Backtrader 开发，包括 backtesting/optimization 支持。”*

### 祖鲁昆特

*   [http://zuluquant.com/ （显然不见了）](http://zuluquant.com/)

 *将*backtrader*列为使用的技术之一，并在社区中发布了一份工作邀请

## 分配

### FreeBSD 端口

*   [FreeBSD](https://svnweb.freebsd.org/ports/head/finance/py-backtrader)

 *### NetBSD

*   [NetBSD](https://ftp.netbsd.org/pub/pkgsrc/current/pkgsrc/finance/py-backtrader/README.html)************