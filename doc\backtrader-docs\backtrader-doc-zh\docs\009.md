# 视频

> 原文： [https://www.backtrader.com/home/<USER>/videos/](https://www.backtrader.com/home/<USER>/videos/)

### [使用 Python 和 GUI 项目的 backtrader 概述](https://www.youtube.com/watch?v=8Y9OXA8gyjg)

 *### [借助 Backtrader 框架](https://www.youtube.com/watch?v=m6b4Ti4P2HA)*在 Python 中实现回溯测试策略*

 *### [Python Backtrader 入门](https://www.youtube.com/watch?v=6UqjZmN_mwY)

 *### [使用 Python3 和 GUI 项目的反向交易者概述](https://www.youtube.com/watch?v=6zXUgkV92Rw)

 *### [教程：Python 算法交易的深度强化学习](https://www.youtube.com/watch?v=_O4T5Vjmgeo)

 *### [教程：如何用 Python](https://www.youtube.com/watch?v=1V0_oat-HEw)*回溯测试比特币交易策略*

 *### [使用 Backtrader 框架](https://youtu.be/m6b4Ti4P2HA)*的回溯测试策略*

 *### [Python 中 algo 交易最佳回溯测试框架](https://youtu.be/ApV7Fpwr_fg)

 *### Python 和 BAcktrader 的算法交易

*   [第一部分](https://www.youtube.com/watch?v=UNkH1TQl7qo)

**   [第二部分](https://www.youtube.com/watch?v=5VU3CJMuk0w)

    **   [第三部分](https://www.youtube.com/watch?v=K8buXUxEfMc)**********