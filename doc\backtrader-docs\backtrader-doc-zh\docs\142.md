# 笔记本-自动内联打印

> 原文： [https://www.backtrader.com/blog/posts/2016-09-17-notebook-inline/notebook-inline/](https://www.backtrader.com/blog/posts/2016-09-17-notebook-inline/notebook-inline/)

******** 版增加了在*Jupyter 笔记本*中运行时的自动内联打印。

关于 backtrader 的一些问题显示，人们在*笔记本*中使用该平台，支持该平台并将其作为默认行为应该会使事情保持一致。

如果希望采用上述行为，并且必须单独绘制图形，只需执行以下操作：

```py
import backtrader as bt

...

cerebro.run()

...

cerebro.plot(iplot=False) 
```

当然，如果从脚本运行或以交互方式运行，`matplotlib`的默认打印后端将像以前一样使用，它将在单独的窗口中打印图表。