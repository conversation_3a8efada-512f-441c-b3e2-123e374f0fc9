# 交易日历

> 原文： [https://www.backtrader.com/docu/tradingcalendar/tradingcalendar/](https://www.backtrader.com/docu/tradingcalendar/tradingcalendar/)

发布版`1.9.42.116`增加了对*交易日历*的支持。例如，在以下场景中重新采样时，这非常有用：

*   每日到每周的重新采样现在可以提供每周的酒吧和一周的最后一个酒吧。

    这是因为交易日历可以提前确定本周的*下一个交易*日和*最后一个交易日*

*   当会话结束时间不是常规会话结束时间（可以指定给数据馈送）时，从每天到每天的重新采样

## 交易日历界面

有一个基类`TradingCalendarBase`，用作任何交易日历的基类。它定义了两（2）种必须重写的方法：

```py
class TradingCalendarBase(with_metaclass(MetaParams, object)):
    def _nextday(self, day):
        '''
 Returns the next trading day (datetime/date instance) after ``day``
 (datetime/date instance) and the isocalendar components

 The return value is a tuple with 2 components: (nextday, (y, w, d))
 where (y, w, d)
 '''
        raise NotImplementedError

    def schedule(self, day):
        '''
 Returns a tuple with the opening and closing times (``datetime.time``)
 for the given ``date`` (``datetime/date`` instance)
 '''
        raise NotImplementedError 
```

## 启动位置

### PandasMarketCalendar

此实现基于一个整洁的包，该包是 Quantopian 提供的初始功能的衍生产品。软件包位于：[熊猫市场日历](https://github.com/rsheftel/pandas_market_calendars)，安装方便：

```py
pip install pandas_market_calendars 
```

该实现具有以下接口：

```py
class PandasMarketCalendar(TradingCalendarBase):
    '''
 Wrapper of ``pandas_market_calendars`` for a trading calendar. The package
 ``pandas_market_calendar`` must be installed

 Params:

 - ``calendar`` (default ``None``)

 The param ``calendar`` accepts the following:

 - string: the name of one of the calendars supported, for example
 `NYSE`. The wrapper will attempt to get a calendar instance

 - calendar instance: as returned by ``get_calendar('NYSE')``

 - ``cachesize`` (default ``365``)

 Number of days to cache in advance for lookup

 See also:

 - https://github.com/rsheftel/pandas_market_calendars

 - http://pandas-market-calendars.readthedocs.io/

 '''
    params = (
        ('calendar', None),  # A pandas_market_calendars instance or exch name
        ('cachesize', 365),  # Number of days to cache in advance
    ) 
```

### 交易日历

此实现允许通过指定假日、开始日期、非交易工作日以及开盘和收盘时间，使用自行收集的信息构建日历：

```py
class TradingCalendar(TradingCalendarBase):
    '''
 Wrapper of ``pandas_market_calendars`` for a trading calendar. The package
 ``pandas_market_calendar`` must be installed

 Params:

 - ``open`` (default ``time.min``)

 Regular start of the session

 - ``close`` (default ``time.max``)

 Regular end of the session

 - ``holidays`` (default ``[]``)

 List of non-trading days (``datetime.datetime`` instances)

 - ``earlydays`` (default ``[]``)

 List of tuples determining the date and opening/closing times of days
 which do not conform to the regular trading hours where each tuple has
 (``datetime.datetime``, ``datetime.time``, ``datetime.time`` )

 - ``offdays`` (default ``ISOWEEKEND``)

 A list of weekdays in ISO format (Monday: 1 -> Sunday: 7) in which the
 market doesn't trade. This is usually Saturday and Sunday and hence the
 default

 '''
    params = (
        ('open', time.min),
        ('close', _time_max),
        ('holidays', []),  # list of non trading days (date)
        ('earlydays', []),  # list of tuples (date, opentime, closetime)
        ('offdays', ISOWEEKEND),  # list of non trading (isoweekdays)
    ) 
```

## 使用模式

### 全球贸易日历

通过`Cerebro`可以为所有数据源添加默认的全局日历，除非为数据源指定了一个日历：

```py
def addcalendar(self, cal):
    '''Adds a global trading calendar to the system. Individual data feeds
 may have separate calendars which override the global one

 ``cal`` can be an instance of ``TradingCalendar`` a string or an
 instance of ``pandas_market_calendars``. A string will be will be
 instantiated as a ``PandasMarketCalendar`` (which needs the module
 ``pandas_market_calendar`` installed in the system.

 If a subclass of `TradingCalendarBase` is passed (not an instance) it
 will be instantiated
 ''' 
```

### 每个数据源

按照`addcalendar`中所述的相同约定指定`calendar`参数。

例如：

```py
...
data = bt.feeds.YahooFinanceData(dataname='YHOO', calendar='NYSE', ...)
cerebro.adddata(data)
... 
```

## 例子

### 每日至每周

让我们看看下面的代码运行示例。2016 年，复活节星期五（2016-03-25）也是`NYSE`的一个节日。如果样本在没有交易日历的情况下运行，那么让我们看看该日期前后会发生什么。

在这种情况下，从每天到每周进行重新采样（使用`YHOO`和 2016 年的每日数据）：

```py
$ ./tcal.py

...
Strategy len 56 datetime 2016-03-23 Data0 len 56 datetime 2016-03-23 Data1 len 11 datetime 2016-03-18
Strategy len 57 datetime 2016-03-24 Data0 len 57 datetime 2016-03-24 Data1 len 11 datetime 2016-03-18
Strategy len 58 datetime 2016-03-28 Data0 len 58 datetime 2016-03-28 Data1 len 12 datetime 2016-03-24
... 
```

在该输出中，1<sup>st</sup>日期是策略进行的核算。第 2<sup>和第</sup>日期是每日的日期

如预期，本周将于 2016 年 3 月 24 日（星期四）结束，但如果没有交易日历，重采样代码将无法识别，并交付日期为 2016 年 3 月 18 日（前一周）的重采样条。当交易转到 2016-03-28（星期一）时，重采样器检测到周变化，并提供 2016-03-24 的重采样器。

相同，但在`NYSE`中使用`PandasMarketCalendar`（并添加一个绘图）

```py
$ ./tcal.py --plot --pandascal NYSE

...
Strategy len 56 datetime 2016-03-23 Data0 len 56 datetime 2016-03-23 Data1 len 11 datetime 2016-03-18
Strategy len 57 datetime 2016-03-24 Data0 len 57 datetime 2016-03-24 Data1 len 12 datetime 2016-03-24
Strategy len 58 datetime 2016-03-28 Data0 len 58 datetime 2016-03-28 Data1 len 12 datetime 2016-03-24
... 
```

有一个变化！多亏了日历，重采样器知道 2016-03-24 这一周结束了，并在同一天交付了 2016-03-24 相应的每周重采样条。

还有情节。

[![!image](img/389f5b9fa5ffe73e6704446bb49b53bb.png)](../dayly-weekly-calendar.png)

由于信息可能不一定适用于每个市场，因此也可以制作日历。对于`NYSE`和`2016`来说，它看起来像：

```py
class NYSE_2016(bt.TradingCalendar):
    params = dict(
        holidays=[
            datetime.date(2016, 1, 1),
            datetime.date(2016, 1, 18),
            datetime.date(2016, 2, 15),
            datetime.date(2016, 3, 25),
            datetime.date(2016, 5, 30),
            datetime.date(2016, 7, 4),
            datetime.date(2016, 9, 5),
            datetime.date(2016, 11, 24),
            datetime.date(2016, 12, 26),
        ]
    ) 
```

复活节星期五（2016-03-25）被列为节假日之一。现在运行示例：

```py
$ ./tcal.py --plot --owncal

...
Strategy len 56 datetime 2016-03-23 Data0 len 56 datetime 2016-03-23 Data1 len 11 datetime 2016-03-18
Strategy len 57 datetime 2016-03-24 Data0 len 57 datetime 2016-03-24 Data1 len 12 datetime 2016-03-24
Strategy len 58 datetime 2016-03-28 Data0 len 58 datetime 2016-03-28 Data1 len 12 datetime 2016-03-24
... 
```

通过精心制作的日历定义也得到了同样的结果。

### 每天几分钟

利用一些私人日内数据和市场在 2016 年 11 月 25 日早盘收盘的知识（感恩节后的第二天，市场在`US/Eastern`时区 13:00 收盘），另一次测试运行，这次使用 2<sup>和</sup>样本。

笔记

源数据直接取自显示的数据，并且在`CET`时间内，即使相关资产`YHOO`在美国交易。数据馈送的代码中使用`tzinput='CET'`和`tz='US/Eastern'`，以便平台适当转换输入并显示输出

第一，没有交易日历

```py
$ ./tcal-intra.py

...
Strategy len 6838 datetime 2016-11-25 18:00:00 Data0 len 6838 datetime 2016-11-25 13:00:00 Data1 len 21 datetime 2016-11-23 16:00:00
Strategy len 6839 datetime 2016-11-25 18:01:00 Data0 len 6839 datetime 2016-11-25 13:01:00 Data1 len 21 datetime 20 16-11-23 16:00:00
Strategy len 6840 datetime 2016-11-28 14:31:00 Data0 len 6840 datetime 2016-11-28 09:31:00 Data1 len 22 datetime 2016-11-25 16:00:00
Strategy len 6841 datetime 2016-11-28 14:32:00 Data0 len 6841 datetime 2016-11-28 09:32:00 Data1 len 22 datetime 2016-11-25 16:00:00
... 
```

正如所料，当天在`13:00`提前结束，但重采样器不知道（正式会议在`16:00`结束），并从前一天（2016-11-23）开始继续交付重采样器，新的重采样器在下一个交易日（2016-11-28）首次交付，日期为 2016-11-25。

笔记

数据在`13:01`处有一个额外的分钟栏，这可能是由于拍卖过程在市场收盘时间后提供了最后一个价格。

我们可以向流中添加一个过滤器，以过滤掉会话时间之外的条（过滤器将从交易日历中找到）

但这不是这个样本的重点。

使用`PandasMarketCalendar`实例执行相同的运行：

```py
$ ./tcal-intra.py --pandascal NYSE

...
Strategy len 6838 datetime 2016-11-25 18:00:00 Data0 len 6838 datetime 2016-11-25 13:00:00 Data1 len 15 datetime 2016-11-25 13:00:00
Strategy len 6839 datetime 2016-11-25 18:01:00 Data0 len 6839 datetime 2016-11-25 13:01:00 Data1 len 15 datetime 2016-11-25 13:00:00
Strategy len 6840 datetime 2016-11-28 14:31:00 Data0 len 6840 datetime 2016-11-28 09:31:00 Data1 len 15 datetime 2016-11-25 13:00:00
Strategy len 6841 datetime 2016-11-28 14:32:00 Data0 len 6841 datetime 2016-11-28 09:32:00 Data1 len 15 datetime 2016-11-25 13:00:00
... 
```

现在，2016-11-25 的每日条形图是在当日 1 分钟提要在 13:00 到达 2016-11-25 时交付的（让我们忽略 13:01 条形图），因为交易日历告诉重新采样代码这一天已经结束。

让我们添加一个精心编制的定义。与前面相同，但扩展了一些`earlydays`

```py
class NYSE_2016(bt.TradingCalendar):
    params = dict(
        holidays=[
            datetime.date(2016, 1, 1),
            datetime.date(2016, 1, 18),
            datetime.date(2016, 2, 15),
            datetime.date(2016, 3, 25),
            datetime.date(2016, 5, 30),
            datetime.date(2016, 7, 4),
            datetime.date(2016, 9, 5),
            datetime.date(2016, 11, 24),
            datetime.date(2016, 12, 26),
        ],
        earlydays=[
            (datetime.date(2016, 11, 25),
             datetime.time(9, 30), datetime.time(13, 1))
        ],
        open=datetime.time(9, 30),
        close=datetime.time(16, 0),
    ) 
```

跑步：

```py
$ ./tcal-intra.py --owncal

...
Strategy len 6838 datetime 2016-11-25 18:00:00 Data0 len 6838 datetime 2016-11-25 13:00:00 Data1 len 15 datetime 2016-11-23 16:00:00
Strategy len 6839 datetime 2016-11-25 18:01:00 Data0 len 6839 datetime 2016-11-25 13:01:00 Data1 len 16 datetime 2016-11-25 13:01:00
Strategy len 6840 datetime 2016-11-28 14:31:00 Data0 len 6840 datetime 2016-11-28 09:31:00 Data1 len 16 datetime 2016-11-25 13:01:00
Strategy len 6841 datetime 2016-11-28 14:32:00 Data0 len 6841 datetime 2016-11-28 09:32:00 Data1 len 16 datetime 2016-11-25 13:01:00
... 
```

热心的读者会注意到，精心编制的定义包含将`13:01`（带`datetime.time(13, 1)`）定义为 2016-11-25 这短短一天的会议结束。这只是为了展示手工制作的`TradingCalendar`是如何帮助安装物品的。

现在，2016-11-25 的每日重采样酒吧与 1 分钟酒吧于 13:01 交付。

### 战略的额外奖金

第一个`datetime`，属于策略的那个，总是在不同的时区，实际上是`UTC`。此外，此版本`1.9.42.116`也可以同步。以下参数已添加到`Cerebro`（在实例化期间使用或与`cerebro.run`一起使用）

```py
- ``tz`` (default: ``None``)

  Adds a global timezone for strategies. The argument ``tz`` can be

    - ``None``: in this case the datetime displayed by strategies will be
      in UTC, which has been always the standard behavior

    - ``pytz`` instance. It will be used as such to convert UTC times to
      the chosen timezone

    - ``string``. Instantiating a ``pytz`` instance will be attempted.

    - ``integer``. Use, for the strategy, the same timezone as the
      corresponding ``data`` in the ``self.datas`` iterable (``0`` would
      use the timezone from ``data0``) 
```

`cerebro.addtz`方法也支持：

```py
def addtz(self, tz):
    '''
 This can also be done with the parameter ``tz``

 Adds a global timezone for strategies. The argument ``tz`` can be

 - ``None``: in this case the datetime displayed by strategies will be
 in UTC, which has been always the standard behavior

 - ``pytz`` instance. It will be used as such to convert UTC times to
 the chosen timezone

 - ``string``. Instantiating a ``pytz`` instance will be attempted.

 - ``integer``. Use, for the strategy, the same timezone as the
 corresponding ``data`` in the ``self.datas`` iterable (``0`` would
 use the timezone from ``data0``)

 ''' 
```

重复日内样本的最后一次运行，并对`tz`使用`0`（与`data0`时区同步），以下是聚焦于上述相同日期和时间的输出：

```py
$ ./tcal-intra.py --owncal --cerebro tz=0

...
Strategy len 6838 datetime 2016-11-25 13:00:00 Data0 len 6838 datetime 2016-11-25 13:00:00 Data1 len 15 datetime 2016-11-23 16:00:00
Strategy len 6839 datetime 2016-11-25 13:01:00 Data0 len 6839 datetime 2016-11-25 13:01:00 Data1 len 16 datetime 2016-11-25 13:01:00
Strategy len 6840 datetime 2016-11-28 09:31:00 Data0 len 6840 datetime 2016-11-28 09:31:00 Data1 len 16 datetime 2016-11-25 13:01:00
Strategy len 6841 datetime 2016-11-28 09:32:00 Data0 len 6841 datetime 2016-11-28 09:32:00 Data1 len 16 datetime 2016-11-25 13:01:00
... 
```

时间戳现在与时区对齐。

## 示例用法（tcal.py）

```py
$ ./tcal.py --help
usage: tcal.py [-h] [--data0 DATA0] [--offline] [--fromdate FROMDATE]
               [--todate TODATE] [--cerebro kwargs] [--broker kwargs]
               [--sizer kwargs] [--strat kwargs] [--plot [kwargs]]
               [--pandascal PANDASCAL | --owncal]
               [--timeframe {Weeks,Months,Years}]

Trading Calendar Sample

optional arguments:
  -h, --help            show this help message and exit
  --data0 DATA0         Data to read in (default: YHOO)
  --offline             Read from disk with same name as ticker (default:
                        False)
  --fromdate FROMDATE   Date[time] in YYYY-MM-DD[THH:MM:SS] format (default:
                        2016-01-01)
  --todate TODATE       Date[time] in YYYY-MM-DD[THH:MM:SS] format (default:
                        2016-12-31)
  --cerebro kwargs      kwargs in key=value format (default: )
  --broker kwargs       kwargs in key=value format (default: )
  --sizer kwargs        kwargs in key=value format (default: )
  --strat kwargs        kwargs in key=value format (default: )
  --plot [kwargs]       kwargs in key=value format (default: )
  --pandascal PANDASCAL
                        Name of trading calendar to use (default: )
  --owncal              Apply custom NYSE 2016 calendar (default: False)
  --timeframe {Weeks,Months,Years}
                        Timeframe to resample to (default: Weeks) 
```

## 示例用法（tcal intra.py）

```py
$ ./tcal-intra.py --help
usage: tcal-intra.py [-h] [--data0 DATA0] [--fromdate FROMDATE]
                     [--todate TODATE] [--cerebro kwargs] [--broker kwargs]
                     [--sizer kwargs] [--strat kwargs] [--plot [kwargs]]
                     [--pandascal PANDASCAL | --owncal] [--timeframe {Days}]

Trading Calendar Sample

optional arguments:
  -h, --help            show this help message and exit
  --data0 DATA0         Data to read in (default: yhoo-2016-11.csv)
  --fromdate FROMDATE   Date[time] in YYYY-MM-DD[THH:MM:SS] format (default:
                        2016-01-01)
  --todate TODATE       Date[time] in YYYY-MM-DD[THH:MM:SS] format (default:
                        2016-12-31)
  --cerebro kwargs      kwargs in key=value format (default: )
  --broker kwargs       kwargs in key=value format (default: )
  --sizer kwargs        kwargs in key=value format (default: )
  --strat kwargs        kwargs in key=value format (default: )
  --plot [kwargs]       kwargs in key=value format (default: )
  --pandascal PANDASCAL
                        Name of trading calendar to use (default: )
  --owncal              Apply custom NYSE 2016 calendar (default: False)
  --timeframe {Days}    Timeframe to resample to (default: Days) 
```

## 示例代码（tcal.py）

```py
from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

import argparse
import datetime

import backtrader as bt

class NYSE_2016(bt.TradingCalendar):
    params = dict(
        holidays=[
            datetime.date(2016, 1, 1),
            datetime.date(2016, 1, 18),
            datetime.date(2016, 2, 15),
            datetime.date(2016, 3, 25),
            datetime.date(2016, 5, 30),
            datetime.date(2016, 7, 4),
            datetime.date(2016, 9, 5),
            datetime.date(2016, 11, 24),
            datetime.date(2016, 12, 26),
        ]
    )

class St(bt.Strategy):
    params = dict(
    )

    def __init__(self):
        pass

    def start(self):
        self.t0 = datetime.datetime.utcnow()

    def stop(self):
        t1 = datetime.datetime.utcnow()
        print('Duration:', t1 - self.t0)

    def prenext(self):
        self.next()

    def next(self):
        print('Strategy len {} datetime {}'.format(
            len(self), self.datetime.date()), end=' ')

        print('Data0 len {} datetime {}'.format(
            len(self.data0), self.data0.datetime.date()), end=' ')

        if len(self.data1):
            print('Data1 len {} datetime {}'.format(
                len(self.data1), self.data1.datetime.date()))
        else:
            print()

def runstrat(args=None):
    args = parse_args(args)

    cerebro = bt.Cerebro()

    # Data feed kwargs
    kwargs = dict()

    # Parse from/to-date
    dtfmt, tmfmt = '%Y-%m-%d', 'T%H:%M:%S'
    for a, d in ((getattr(args, x), x) for x in ['fromdate', 'todate']):
        if a:
            strpfmt = dtfmt + tmfmt * ('T' in a)
            kwargs[d] = datetime.datetime.strptime(a, strpfmt)

    YahooData = bt.feeds.YahooFinanceData
    if args.offline:
        YahooData = bt.feeds.YahooFinanceCSVData  # change to read file

    # Data feed
    data0 = YahooData(dataname=args.data0, **kwargs)
    cerebro.adddata(data0)

    d1 = cerebro.resampledata(data0,
                              timeframe=getattr(bt.TimeFrame, args.timeframe))
    d1.plotinfo.plotmaster = data0
    d1.plotinfo.sameaxis = True

    if args.pandascal:
        cerebro.addcalendar(args.pandascal)
    elif args.owncal:
        cerebro.addcalendar(NYSE_2016)

    # Broker
    cerebro.broker = bt.brokers.BackBroker(**eval('dict(' + args.broker + ')'))

    # Sizer
    cerebro.addsizer(bt.sizers.FixedSize, **eval('dict(' + args.sizer + ')'))

    # Strategy
    cerebro.addstrategy(St, **eval('dict(' + args.strat + ')'))

    # Execute
    cerebro.run(**eval('dict(' + args.cerebro + ')'))

    if args.plot:  # Plot if requested to
        cerebro.plot(**eval('dict(' + args.plot + ')'))

def parse_args(pargs=None):
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description=(
            'Trading Calendar Sample'
        )
    )

    parser.add_argument('--data0', default='YHOO',
                        required=False, help='Data to read in')

    parser.add_argument('--offline', required=False, action='store_true',
                        help='Read from disk with same name as ticker')

    # Defaults for dates
    parser.add_argument('--fromdate', required=False, default='2016-01-01',
                        help='Date[time] in YYYY-MM-DD[THH:MM:SS] format')

    parser.add_argument('--todate', required=False, default='2016-12-31',
                        help='Date[time] in YYYY-MM-DD[THH:MM:SS] format')

    parser.add_argument('--cerebro', required=False, default='',
                        metavar='kwargs', help='kwargs in key=value format')

    parser.add_argument('--broker', required=False, default='',
                        metavar='kwargs', help='kwargs in key=value format')

    parser.add_argument('--sizer', required=False, default='',
                        metavar='kwargs', help='kwargs in key=value format')

    parser.add_argument('--strat', required=False, default='',
                        metavar='kwargs', help='kwargs in key=value format')

    parser.add_argument('--plot', required=False, default='',
                        nargs='?', const='{}',
                        metavar='kwargs', help='kwargs in key=value format')

    pgroup = parser.add_mutually_exclusive_group(required=False)
    pgroup.add_argument('--pandascal', required=False, action='store',
                        default='', help='Name of trading calendar to use')

    pgroup.add_argument('--owncal', required=False, action='store_true',
                        help='Apply custom NYSE 2016 calendar')

    parser.add_argument('--timeframe', required=False, action='store',
                        default='Weeks', choices=['Weeks', 'Months', 'Years'],
                        help='Timeframe to resample to')

    return parser.parse_args(pargs)

if __name__ == '__main__':
    runstrat() 
```

## 示例代码（tcal intra.py）

```py
from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

import argparse
import datetime

import backtrader as bt

class NYSE_2016(bt.TradingCalendar):
    params = dict(
        holidays=[
            datetime.date(2016, 1, 1),
            datetime.date(2016, 1, 18),
            datetime.date(2016, 2, 15),
            datetime.date(2016, 3, 25),
            datetime.date(2016, 5, 30),
            datetime.date(2016, 7, 4),
            datetime.date(2016, 9, 5),
            datetime.date(2016, 11, 24),
            datetime.date(2016, 12, 26),
        ],
        earlydays=[
            (datetime.date(2016, 11, 25),
             datetime.time(9, 30), datetime.time(13, 1))
        ],
        open=datetime.time(9, 30),
        close=datetime.time(16, 0),
    )

class St(bt.Strategy):
    params = dict(
    )

    def __init__(self):
        pass

    def prenext(self):
        self.next()

    def next(self):
        print('Strategy len {} datetime {}'.format(
            len(self), self.datetime.datetime()), end=' ')

        print('Data0 len {} datetime {}'.format(
            len(self.data0), self.data0.datetime.datetime()), end=' ')

        if len(self.data1):
            print('Data1 len {} datetime {}'.format(
                len(self.data1), self.data1.datetime.datetime()))
        else:
            print()

def runstrat(args=None):
    args = parse_args(args)

    cerebro = bt.Cerebro()

    # Data feed kwargs
    # kwargs = dict(tz='US/Eastern')
    # import pytz
    # tz = tzinput = pytz.timezone('Europe/Berlin')
    tzinput = 'Europe/Berlin'
    # tz = tzinput
    tz = 'US/Eastern'
    kwargs = dict(tzinput=tzinput, tz=tz)

    # Parse from/to-date
    dtfmt, tmfmt = '%Y-%m-%d', 'T%H:%M:%S'
    for a, d in ((getattr(args, x), x) for x in ['fromdate', 'todate']):
        if a:
            strpfmt = dtfmt + tmfmt * ('T' in a)
            kwargs[d] = datetime.datetime.strptime(a, strpfmt)

    # Data feed
    data0 = bt.feeds.BacktraderCSVData(dataname=args.data0, **kwargs)
    cerebro.adddata(data0)

    d1 = cerebro.resampledata(data0,
                              timeframe=getattr(bt.TimeFrame, args.timeframe))
    # d1.plotinfo.plotmaster = data0
    # d1.plotinfo.sameaxis = False

    if args.pandascal:
        cerebro.addcalendar(args.pandascal)
    elif args.owncal:
        cerebro.addcalendar(NYSE_2016())  # or NYSE_2016() to pass an instance

    # Broker
    cerebro.broker = bt.brokers.BackBroker(**eval('dict(' + args.broker + ')'))

    # Sizer
    cerebro.addsizer(bt.sizers.FixedSize, **eval('dict(' + args.sizer + ')'))

    # Strategy
    cerebro.addstrategy(St, **eval('dict(' + args.strat + ')'))

    # Execute
    cerebro.run(**eval('dict(' + args.cerebro + ')'))

    if args.plot:  # Plot if requested to
        cerebro.plot(**eval('dict(' + args.plot + ')'))

def parse_args(pargs=None):
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description=(
            'Trading Calendar Sample'
        )
    )

    parser.add_argument('--data0', default='yhoo-2016-11.csv',
                        required=False, help='Data to read in')

    # Defaults for dates
    parser.add_argument('--fromdate', required=False, default='2016-01-01',
                        help='Date[time] in YYYY-MM-DD[THH:MM:SS] format')

    parser.add_argument('--todate', required=False, default='2016-12-31',
                        help='Date[time] in YYYY-MM-DD[THH:MM:SS] format')

    parser.add_argument('--cerebro', required=False, default='',
                        metavar='kwargs', help='kwargs in key=value format')

    parser.add_argument('--broker', required=False, default='',
                        metavar='kwargs', help='kwargs in key=value format')

    parser.add_argument('--sizer', required=False, default='',
                        metavar='kwargs', help='kwargs in key=value format')

    parser.add_argument('--strat', required=False, default='',
                        metavar='kwargs', help='kwargs in key=value format')

    parser.add_argument('--plot', required=False, default='',
                        nargs='?', const='{}',
                        metavar='kwargs', help='kwargs in key=value format')

    pgroup = parser.add_mutually_exclusive_group(required=False)
    pgroup.add_argument('--pandascal', required=False, action='store',
                        default='', help='Name of trading calendar to use')

    pgroup.add_argument('--owncal', required=False, action='store_true',
                        help='Apply custom NYSE 2016 calendar')

    parser.add_argument('--timeframe', required=False, action='store',
                        default='Days', choices=['Days'],
                        help='Timeframe to resample to')

    return parser.parse_args(pargs)

if __name__ == '__main__':
    runstrat() 
```