# 数据重采样

> 原文： [https://www.backtrader.com/blog/posts/2015-08-23-data-resampling/data-resampling/](https://www.backtrader.com/blog/posts/2015-08-23-data-resampling/data-resampling/)

当数据仅在一个时间段内可用，并且必须针对不同的时间段进行分析时，是时候进行一些重采样了。

“重采样”实际上应该被称为“上采样”，因为从源时间段到更大的时间段（例如：几天到几周）

“下采样”尚不可能。

`backtrader`内置了对重新采样的支持，通过智能命名为`DataResampler`的过滤对象传递原始数据。

该类有两个功能：

*   改变时间表

*   压杆

为此，`DataResampler`在施工期间使用标准`feed.DataBase`参数：

*   `timeframe`（默认值：bt.TimeFrame.Days）

    有用的目标时间范围必须等于或大于源时间范围

*   `compression`（默认值：1）

    将所选值“n”压缩到 1 巴

让我们看一个从每天到每周的手工脚本示例：

```py
$ ./data-resampling.py --timeframe weekly --compression 1 
```

输出：

[![!image](img/d4fb746f392cdd09d8df33d275cac149.png)](../resample-daily-weekly.png)

我们可以将其与原始每日数据进行比较：

```py
$ ./data-resampling.py --timeframe daily --compression 1 
```

输出：

[![!image](img/15344c8ee6a11e8dcaa02f05f63aa04f.png)](../resample-daily-daily.png)

通过执行以下步骤，可以完成此魔术：

*   像往常一样加载数据

*   将数据输入到具有所需数据的`DataResampler`

    *   时间框架

    *   压缩

示例中的代码（底部的整个脚本）。

```py
 # Load the Data
    datapath = args.dataname or '../datas/sample/2006-day-001.txt'
    data = btfeeds.BacktraderCSVData(
        dataname=datapath)

    # Handy dictionary for the argument timeframe conversion
    tframes = dict(
        daily=bt.TimeFrame.Days,
        weekly=bt.TimeFrame.Weeks,
        monthly=bt.TimeFrame.Months)

    # Resample the data
    data_resampled = bt.DataResampler(
        dataname=data,
        timeframe=tframes[args.timeframe],
        compression=args.compression)

    # Add the resample data instead of the original
    cerebro.adddata(data_resampled) 
```

最后一个示例中，我们首先将时间范围从每天更改为每周，然后应用 3:1 压缩：

```py
$ ./data-resampling.py --timeframe weekly --compression 3 
```

输出：

[![!image](img/4f595a25f1b4ef03bd4d4973e3995791.png)](../resample-daily-weekly-3.png)

从原来的 256 个每日酒吧，我们最终有 18 个 3 周酒吧。分项数字：

*   52 周

*   52/3=17.33，因此为 18 巴

不用花太多时间。当然，日内数据也可以重新采样。

重采样测试脚本的示例代码。

```py
from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

import argparse

import backtrader as bt
import backtrader.feeds as btfeeds

def runstrat():
    args = parse_args()

    # Create a cerebro entity
    cerebro = bt.Cerebro(stdstats=False)

    # Add a strategy
    cerebro.addstrategy(bt.Strategy)

    # Load the Data
    datapath = args.dataname or '../datas/sample/2006-day-001.txt'
    data = btfeeds.BacktraderCSVData(
        dataname=datapath)

    # Handy dictionary for the argument timeframe conversion
    tframes = dict(
        daily=bt.TimeFrame.Days,
        weekly=bt.TimeFrame.Weeks,
        monthly=bt.TimeFrame.Months)

    # Resample the data
    data_resampled = bt.DataResampler(
        dataname=data,
        timeframe=tframes[args.timeframe],
        compression=args.compression)

    # Add the resample data instead of the original
    cerebro.adddata(data_resampled)

    # Run over everything
    cerebro.run()

    # Plot the result
    cerebro.plot(style='bar')

def parse_args():
    parser = argparse.ArgumentParser(
        description='Pandas test script')

    parser.add_argument('--dataname', default='', required=False,
                        help='File Data to Load')

    parser.add_argument('--timeframe', default='weekly', required=False,
                        choices=['daily', 'weekly', 'monhtly'],
                        help='Timeframe to resample to')

    parser.add_argument('--compression', default=1, required=False, type=int,
                        help='Compress n bars into 1')

    return parser.parse_args()

if __name__ == '__main__':
    runstrat() 
```