---
title: 解析響應包
id: how-to-parse-response
slug: /socket/protocol/response
sidebar_position: 5
---

當服務端收到客戶端的請求包後必須響應一個響應包回來

:::info
當包頭中的 `type` 值爲 `2` 時，數據包爲請求包
:::

## 結構

```
 0                   1                   2                   3
 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
| type=2|v|g|re.|    cmd_code   |           request_id          |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                               |  status_code  |    body_len   |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|            body_len           |                               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+                               +
|                       body(by body_len)                       |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                                                               |
+                        nonce(optional)                        +
|                                                               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                                                               |
+                                                               +
|                                                               |
+                      signature(optional)                      +
|                                                               |
+                                                               +
|                                                               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
```

字段說明：

| 字段       | 長度 (bit)                 | 長度（字節）| 說明                                                                               |
| ---------- | -------------------------- | ------------ | ---------------------------------------------------------------------------------- |
| cmd_code   | 8                          | 1            | 指令 cmd 值                                                                        |
| request_id | 32(uint32)                 | 4            | 請求 id，同一個連接的 id 需要唯一，從 1 開始，到達 4294967295 後從新開始。         |
| status     | 8(uint8)                   | 1            | 狀態碼 `0` - 成功；參考狀態碼錶                                                    |
| body_len   | 24(uint32)                 | 3            | `body` 長度，單位：字節，最大 16 MB 數據；如果 gzip 爲 1，該值爲 body 壓縮後的長度 |
| body       | 可變長度，由 body_len 決定 | 可變長度     | `body`，最大 16 MB                                                                 |
| nonce      | 64                         | 8            | 僅當包頭中的 `verify` 爲 1 時存在                                                  |
| signature  | 128                        | 16           | 僅當包頭中的 verify 爲 1 時存在                                                    |

## 響應包狀態碼

響應包有狀態說明：

| 值  | 標識                  | 說明                                      |
| --- | --------------------- | ----------------------------------------- |
| 0   | SUCCESS               | 成功，類似於 HTTP 200                     |
| 1   | SERVER_TIMEOUT        | 服務端超時，類似於 HTTP 408               |
| 3   | BAD_REQUEST           | 請求錯誤，通常爲參數錯誤，類似於 HTTP 400 |
| 5   | UNAUTHENTICATED       | 鑑權失敗，類似於 HTTP 401                 |
| 7   | SERVER_INTERNAL_ERROR | 服務端內部錯誤，類似於 HTTP 500           |
