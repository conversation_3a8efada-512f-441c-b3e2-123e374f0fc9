@import './marker.css';
@import './button.css';
@import './custom-block.css';
@import './blockquote.css';
@import './vp-code-group.css';
@import './vp-code-title.css';
@import './font.css';
@import './sidebar.css';
@import './custom.scss';
@import './css-var.scss';

html:root {
  --vp-c-brand-1: var(--brand-100);
  --vp-c-brand-2: var(--brand-90);
  --vp-c-brand-3: var(--brand-80);
}

/**
 * Component: Home
 * -------------------------------------------------------------------------- */

 :root {
  --vp-home-hero-name-color: transparent;
  --vp-home-hero-name-background: -webkit-linear-gradient(
    120deg,
    #bd34fe 30%,
    #41d1ff
  );

  --vp-home-hero-image-background-image: linear-gradient(
    -45deg,
    #bd34fe 50%,
    #47caff 50%
  );
  --vp-home-hero-image-filter: blur(44px);
}

@media (min-width: 640px) {
  :root {
    --vp-home-hero-image-filter: blur(56px);
  }
}

@media (min-width: 960px) {
  :root {
    --vp-home-hero-image-filter: blur(68px);
  }
}