# 佣金：信用卡

> 原文： [https://www.backtrader.com/docu/commission-credit/](https://www.backtrader.com/docu/commission-credit/)

在某些情况下，房地产经纪人的现金金额可能会减少，因为资产操作包括利率。示例：

*   卖空股票

*   做多和做空 ETF

这笔费用直接从经纪人账户的现金余额中扣除。但它仍然可以被视为佣金计划的一部分。因此，它已在*反向交易者*中建模。

`CommInfoBase`类（以及`CommissionInfo`主接口对象）已扩展为：

*   两（2）个新参数，允许设定利率并确定是否应仅适用于空头或同时适用于多头和空头

## 参数

*   `interest`（定义：`0.0`）

    如果这不是零，这是持有卖空头寸所收取的年利息。这主要是针对股票卖空

    应用的默认公式：`days * price * size * (interest / 365)`

    必须以绝对值表示：0.05->5%

    笔记

    可以通过重写方法来更改行为：`get_credit_interest`

*   `interest_long`（定义：`False`）

    一些产品，如 ETF，因空头和多头头寸而收取利息。如果 ths 为`True`且`interest`为非零，则利息将在两个方向上收取

## 公式

默认实现将使用以下公式：

```py
days * abs(size) * price * (interest / 365) 
```

哪里：

*   `days`：持仓或上次信用利息计算后经过的天数

## 覆盖公式

为了改变公式，需要对`CommissionInfo`进行子类化。要重写的方法是：

```py
def _get_credit_interest(self, size, price, days, dt0, dt1):
    '''
 This method returns  the cost in terms of credit interest charged by
 the broker.

 In the case of ``size > 0`` this method will only be called if the
 parameter to the class ``interest_long`` is ``True``

 The formulat for the calculation of the credit interest rate is:

 The formula: ``days * price * abs(size) * (interest / 365)``

 Params:
 - ``data``: data feed for which interest is charged

 - ``size``: current position size. > 0 for long positions and < 0 for
 short positions (this parameter will not be ``0``)

 - ``price``: current position price

 - ``days``: number of days elapsed since last credit calculation
 (this is (dt0 - dt1).days)

 - ``dt0``: (datetime.datetime) current datetime

 - ``dt1``: (datetime.datetime) datetime of previous calculation

 ``dt0`` and ``dt1`` are not used in the default implementation and are
 provided as extra input for overridden methods
 ''' 
```

这可能是因为在计算利率时，不考虑周末或银行假日。在这种情况下，这个子类就可以做到这一点

```py
import backtrader as bt

class MyCommissionInfo(bt.CommInfo):

   def _get_credit_interest(self, size, price, days, dt0, dt1):
       return 1.0 * abs(size) * price * (self.p.interest / 365.0) 
```

在这种情况下，在公式中：

*   `days`已被`1.0`取代

因为如果周末/银行假期不计算在内，下一次计算总是在上一次计算之后进行`1`交易 da