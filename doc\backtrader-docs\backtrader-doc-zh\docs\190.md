# CSV 数据源开发

> 原文： [https://www.backtrader.com/blog/posts/2015-08-06-csv-data-feed-development/csv-data-feed-development/](https://www.backtrader.com/blog/posts/2015-08-06-csv-data-feed-development/csv-data-feed-development/)

backtrader 已经提供了一个通用的 CSV 数据源和一些特定的 CSV 数据源。总结：

*   通用 CSVDATA

*   视觉特征数据

*   Yahoo 财务数据（用于在线下载）

*   YahooFinanceCSVData（用于已下载的数据）

*   BacktraderCSVData（内部…用于测试，但可以使用）

但即使这样，最终用户也可能希望开发对特定 CSV 数据源的支持。

通常的座右铭是：“说起来容易做起来难”。实际上，这种结构是为了使它变得容易。

步骤：

*   继承自`backtrader.CSVDataBase`

*   如果需要，定义任何`params`

*   在`start`方法中进行任何初始化

*   按`stop`方法进行任何清理

*   定义实际工作发生的`_loadline`方法

    此方法接收单个参数：linetokens。

    顾名思义，它包含根据`separator`参数（继承自基类）拆分当前行后的标记

    如果完成工作后有新数据……填写相应行并返回`True`

    如果没有可用信息，因此解析结束：返回`False`

    如果读取文件行的幕后代码发现没有更多的行需要解析，则甚至可能不需要返回`False`。

已考虑的事项：

*   打开文件（或接收类似文件的对象）

*   如果指示为存在，则跳过标题行

*   读台词

*   标记行

*   预加载支持（在内存中一次性加载整个数据源）

通常，一个例子胜过一千个需求描述。让我们使用`BacktraderCSVData`中内部定义的 CSV 解析代码的简化版本。这一个不需要初始化或清理（例如，可以打开一个套接字，稍后再关闭它）。

笔记

`backtrader`数据提要包含通常的行业标准提要，这些提要是需要填充的。即：

*   日期时间

*   打开

*   高的

*   低的

*   关

*   体积

*   无息

如果您的策略/算法或简单的数据阅读只需要，例如收盘价，您可以保持其他价格不变（在最终用户代码有机会做任何事情之前，每次迭代都会自动用浮点（'NaN'）值填充它们）。

在此示例中，仅支持每日格式：

```py
import itertools
...
import backtrader import bt

class MyCSVData(bt.CSVDataBase):

    def start(self):
        # Nothing to do for this data feed type
        pass

    def stop(self):
        # Nothing to do for this data feed type
        pass

    def _loadline(self, linetokens):
        i = itertools.count(0)

        dttxt = linetokens[next(i)]
        # Format is YYYY-MM-DD
        y = int(dttxt[0:4])
        m = int(dttxt[5:7])
        d = int(dttxt[8:10])

        dt = datetime.datetime(y, m, d)
        dtnum = date2num(dt)

        self.lines.datetime[0] = dtnum
        self.lines.open[0] = float(linetokens[next(i)])
        self.lines.high[0] = float(linetokens[next(i)])
        self.lines.low[0] = float(linetokens[next(i)])
        self.lines.close[0] = float(linetokens[next(i)])
        self.lines.volume[0] = float(linetokens[next(i)])
        self.lines.openinterest[0] = float(linetokens[next(i)])

        return True 
```

代码要求所有字段都到位并可转换为浮动，但 datetime 除外，它具有固定的 YYYY-MM-DD 格式，可以在不使用`datetime.datetime.strptime`的情况下进行解析。

通过添加几行代码来解释空值、日期格式解析，可以满足更复杂的需求。`GenericCSVData`就是这样做的。

## 买主须知

使用`GenericCSVData`现有的提要和继承可以完成很多工作，以支持格式。

让我们添加对[Sierra Chart](https://www.sierrachart.com)每日格式（始终以 CSV 格式存储）的支持。

定义（通过查看其中一个**.DY'**数据文件：

*   **字段**：日期、开盘、高位、低位、收盘、成交量、开盘利率

    行业标准的和`GenericCSVData`已经支持的顺序相同（也是行业标准）

*   **分离器**：，

*   **日期格式**：YYYY/MM/DD

这些文件的解析器：

```py
class SierraChartCSVData(backtrader.feeds.GenericCSVData):

    params = (('dtformat', '%Y/%m/%d'),) 
```

`params`定义只是重新定义基类中的一个现有参数。在这种情况下，只需更改日期的格式字符串。

等等，**塞拉图**的解析器已经完成。

以下是`GenericCSVData`的参数定义，作为提醒：

```py
class GenericCSVData(feed.CSVDataBase):
    params = (
        ('nullvalue', float('NaN')),
        ('dtformat', '%Y-%m-%d %H:%M:%S'),
        ('tmformat', '%H:%M:%S'),

        ('datetime', 0),
        ('time', -1),
        ('open', 1),
        ('high', 2),
        ('low', 3),
        ('close', 4),
        ('volume', 5),
        ('openinterest', 6),
    ) 
```