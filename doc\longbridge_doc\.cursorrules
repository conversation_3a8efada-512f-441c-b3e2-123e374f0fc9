# Cursor Rules for LongPort OpenAPI Documentation Project

You are assisting with the development of the LongPort OpenAPI documentation website, which uses the following tech stack:

## Tech Stack
- VitePress 2.0.0-alpha.5 (Vue-based static site generator)
- TypeScript (ESNext target)
- Bun as package/project manager
- Markdown for content

## Code Style & Conventions
- No semicolons (semi: false)
- Single quotes for strings (singleQuote: true)
- 2 spaces for indentation (tabWidth: 2, useTabs: false)
- 120 characters line length limit (printWidth: 120)
- Arrow functions with parentheses always (arrowParens: "always")
- ES5-style trailing commas (trailingComma: "es5")

## Internationalization Guidelines
- English (en) is the default locale
- Chinese Simplified (zh-CN) is the secondary locale
- Translations follow the pattern "@lang/@path"
- Both markdown files and configuration are internationalized

## When writing code for this project:
1. Follow the established code style and formatting conventions
2. Maintain the internationalization structure - any new content should support both English and Chinese
3. When modifying VitePress config, keep the structure consistent with the existing configuration
4. Use ES modules (import/export) syntax, not CommonJS
5. Ensure proper TypeScript typing
6. For content changes, update both language versions
7. Follow Vue 3 Composition API conventions for any component development
8. Keep sidebar structure consistent with the existing pattern

## For Markdown content:
1. Use frontmatter to define page metadata
2. Follow the existing heading structure
3. Use relative links for internal navigation
4. Properly format code blocks with language specifiers
5. Use the established layout templates (home, doc, etc.)

## Do not:
1. Mix code styles or formatting
2. Break the internationalization pattern
3. Use outdated Vue 2 or Options API patterns
4. Introduce unnecessary dependencies
5. Use features not supported by VitePress

This project is a documentation website for LongPort's API services, so maintain a professional, clear, and technical writing style while ensuring content is well-organized and accessible to developers. 