# 数据源参考

> 原文： [https://www.backtrader.com/docu/dataautoref/](https://www.backtrader.com/docu/dataautoref/)

### 抽象数据库

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None) 
```

### 反向交易数据

解析用于测试的自定义 CSV 数据。

具体参数：

*   `dataname`：要解析的文件名或类似文件的对象

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* headers (True)

* separator (,) 
```

### CSVDataBase

用于实现 CSV 数据源的类的基类

该类负责打开文件、读取行并标记它们。

子类只需重写：

*   _ 载重线（代币）

`_loadline`的返回值（真/假）将是`_load`的返回值，该返回值已被该基类覆盖

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* headers (True)

* separator (,) 
```

### 链子

类来链接数据

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None) 
```

### 数据克隆

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None) 
```

### 数据填充器

此类将使用来自底层数据源的以下信息位来填补源数据中的空白

*   时间框架和压缩以标注输出栏的尺寸

*   sessionstart 和 sessionend

如果数据馈送在 10:31 和 10:34 之间缺少条形图，且时间范围为分钟，则输出将使用最后一个条形图（10:31）的收盘价填充 10:32 和 10:33 分钟的条形图

酒吧可能会丢失，因为

参数：

```py
* `fill_price` (def: None): if None (or evaluates to False),the
  closing price will be used, else the passed value (which can be
  for example ‘NaN’ to have a missing bar in terms of evaluation but
  present in terms of time

* `fill_vol` (def: NaN): used to fill the volume of missing bars

* `fill_oi` (def: NaN): used to fill the openinterest of missing bars 
```

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* fill_price (None)

* fill_vol (nan)

* fill_oi (nan) 
```

### 数据过滤器

此类从给定数据源中过滤出条形图。除了数据库的标准参数外，它还需要一个可调用的`funcfilter`参数

逻辑：

*   将使用基础数据源调用`funcfilter`

    它可以是任何可调用的

    *   返回值`True`：将使用当前数据源条值

    *   返回值`False`：当前数据源条值将被丢弃

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* funcfilter (None) 
```

### 通用 CSVDATA

根据参数定义的顺序和字段显示来解析 CSV 文件

具体参数（或具体含义）：

*   `dataname`：要解析的文件名或类似文件的对象

*   行参数（datetime、open、high…）采用数值

    值-1 表示 CSV 源中没有该字段

*   如果存在`time`（参数时间>=0），则源包含日期和时间的单独字段，将组合这些字段

*   `nullvalue`

    如果缺少应存在的值（CSV 字段为空），将使用的值

*   `dtformat`：用于解析日期时间 CSV 字段的格式。有关格式，请参阅 python strtime/strftime 文档。

    如果指定了一个数值，它将被解释为

    *   `1`：该值是一个类型为`int`的 Unix 时间戳，表示自 1970 年 1 月 1 日<sup>st</sup>以来的秒数

    *   `2`：该值是类型为`float`的 Unix 时间戳

    如果通过了**可调用**

    *   它将接受字符串并返回 datetime.datetime python 实例
*   `tmformat`：如果“存在”，则用于解析时间 CSV 字段的格式（“时间”CSV 字段的默认值不存在）

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* headers (True)

* separator (,)

* nullvalue (nan)

* dtformat (%Y-%m-%d %H:%M:%S)

* tmformat (%H:%M:%S)

* datetime (0)

* time (-1)

* open (1)

* high (2)

* low (3)

* close (4)

* volume (5)

* openinterest (6) 
```

### IBData

交互式代理数据源。

参数`dataname`中支持以下合同规范：

*   股票代码#股票类型和智能交易所

*   TICKER-STK#股票和智能交易所

*   股票交易所

*   TICKER-STK-EXCHANGE-CURRENCY#Stock

*   TICKER-CFD#CFD 和智能交换

*   股票代码-CFD-EXCHANGE#CFD

*   TICKER-CDF-EXCHANGE-CURRENCY#Stock

*   股票交易所指数

*   TICKER-IND-EXCHANGE-CURRENCY 指数

*   TICKER-YYYYMM-EXCHANGE#未来

*   TICKER-YYYYMM-EXCHANGE-CURRENCY#未来

*   TICKER-YYYYMM-EXCHANGE-CURRENCY-MULT#Future

*   TICKER-FUT-EXCHANGE-CURRENCY-YYYYMM-MULT#未来

*   TICKER-YYYYMM-EXCHANGE-CURRENCY-STRIKE-RIGHT#FOP

*   TICKER-YYYYMM-EXCHANGE-CURRENCY-STRIKE-RIGHT-MULT#FOP

*   TICKER-FOP-EXCHANGE-CURRENCY-YYYYMM-STRIKE-RIGHT#FOP

*   TICKER-FOP-EXCHANGE-CURRENCY-YYYYMM-STRIKE-RIGHT-MULT#FOP

*   CUR1.CUR2-CASH-IDEALPRO#外汇

*   TICKER-YYYYMMDD-EXCHANGE-CURRENCY-STRIKE-RIGHT#OPT

*   TICKER-YYYYMMDD-EXCHANGE-CURRENCY-STRIKE-RIGHT-MULT#OPT

*   TICKER-OPT-EXCHANGE-CURRENCY-YYYYMMDD-STRIKE-RIGHT#OPT

*   TICKER-OPT-EXCHANGE-CURRENCY-YYYYMMDD-STRIKE-RIGHT-MULT#OPT

参数：

*   `sectype`（默认为`STK`）

    如果`dataname`规范中未提供，则应用为*安全类型*的默认值

*   `exchange`（默认为`SMART`）

    如果`dataname`规范中未提供，则应用为*交换*的默认值

*   `currency`（默认为`''`）

    如果`dataname`规范中未提供，则默认值应用为*货币*

*   `historical`（默认为`False`）

    如果设置为`True`，数据馈送将在第一次下载数据后停止。

    标准数据馈送参数`fromdate`和`todate`将用作参考。

    如果请求的持续时间大于 IB 允许的持续时间，则数据馈送将发出多个请求，前提是为数据选择了时间段/压缩。

*   `what`（默认为`None`）

    如果`None`不同资产类型的默认值将用于历史数据请求：

    *   “竞购”现金资产

    *   “交易”任何其他

    如果需要其他值，请检查 IB API 文档

*   `rtbar`（默认为`False`）

    如果`True`将使用互动经纪人提供的`5 Seconds Realtime bars`作为 smalles 勾号。根据文档，它们对应于实时值（一旦 IB 整理和管理）

    如果是`False`，则将使用`RTVolume`价格，该价格基于收到的滴答声。在`CASH`资产的情况下（例如，欧元兑日元）`RTVolume`将始终使用，并从中获得`bid`价格（根据分散在互联网上的文献，IB 的行业事实标准）

    即使设置为`True`，如果数据被重新采样/保存到低于秒/5 的时间段/压缩，也不会使用实时条，因为 IB 不会在低于该水平的情况下为其提供服务

*   `qcheck`（默认为`0.5`）

    如果没有接收到数据，以秒为单位的唤醒时间，以便有机会正确地重新采样/重播数据包，并在链上传递通知

*   `backfill_start`（默认为`True`）

    开始时进行回填。在单个请求中将获取最大可能的历史数据。

*   `backfill`（默认为`True`）

    断开/重新连接循环后进行回填。间隙持续时间将用于下载尽可能少的数据量

*   `backfill_from`（默认为`None`）

    可以传递一个额外的数据源来进行初始层的回填。一旦数据源耗尽，如果需要，将从 IB 进行回填。理想情况下，这意味着要从已存储的源（如磁盘上的文件）进行回填，但不限于此。

*   `latethrough`（默认为`False`）

    如果对数据源进行了重采样/重放，则对于已交付的重采样/重放条，某些标记可能来得太迟。如果这是`True`，那么这些滴答声在任何情况下都会通过。

    查看重采样器文档，看看谁应该考虑这些滴答声。

    如果在`IBStore`实例中将`timeoffset`设置为`False`并且 TWS 服务器时间与本地计算机的时间不同步，则可能会发生这种情况

*   `tradename`（默认值：`None`）适用于`CFD`等特定情况，其中价格由一种资产提供，交易在另一种资产中进行

    *   SPY-STK-SMART-USD->SP500 ETF（将指定为`dataname`）

    *   SPY-CFD-SMART-USD->这是相应的 CFD，不提供价格跟踪，但在这种情况下将是交易资产（指定为`tradename`）

参数中的默认值是允许应用`\`TICKER`, to which the parameter`sectype`(default:`STK`) and`exchange`(default:`SMART`）之类的内容。

像`AAPL`这样的一些资产需要完整的规范，包括`currency`（默认值：“”），而像`TWTR`这样的其他资产可以简单地按原样传递。

*   `AAPL-STK-SMART-USD`将是 dataname 的完整规范

    或者：`IBData`为`IBData(dataname='AAPL', currency='USD')`，使用默认值（`STK`和`SMART`，并将币种改写为`USD`

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.5)

* calendar (None)

* sectype (STK)

* exchange (SMART)

* currency ()

* rtbar (False)

* historical (False)

* what (None)

* useRTH (False)

* backfill_start (True)

* backfill (True)

* backfill_from (None)

* latethrough (False)

* tradename (None) 
```

### 流入 xDB

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* host (127.0.0.1)

* port (8086)

* username (None)

* password (None)

* database (None)

* startdate (None)

* high (high_p)

* low (low_p)

* open (open_p)

* close (close_p)

* volume (volume)

* ointerest (oi) 
```

### MT4CSVData

解析[Metatrader4](https://www.metaquotes.net/en/metatrader4)历史中心 CSV 导出文件。

具体参数（或具体含义）：

*   `dataname`：要解析的文件名或类似文件的对象

*   使用 GenericCSVData 并简单地修改参数

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* headers (True)

* separator (,)

* nullvalue (nan)

* dtformat (%Y.%m.%d)

* tmformat (%H:%M)

* datetime (0)

* time (1)

* open (2)

* high (3)

* low (4)

* close (5)

* volume (6)

* openinterest (-1) 
```

### 奥恩达数据

Oanda 数据源。

参数：

*   `qcheck`（默认为`0.5`）

    如果没有接收到数据，以秒为单位的唤醒时间，以便有机会正确地重新采样/重播数据包，并在链上传递通知

*   `historical`（默认为`False`）

    如果设置为`True`，数据馈送将在第一次下载数据后停止。

    标准数据馈送参数`fromdate`和`todate`将用作参考。

    如果请求的持续时间大于 IB 允许的持续时间，则数据馈送将发出多个请求，前提是为数据选择了时间段/压缩。

*   `backfill_start`（默认为`True`）

    开始时进行回填。在单个请求中将获取最大可能的历史数据。

*   `backfill`（默认为`True`）

    断开/重新连接循环后进行回填。间隙持续时间将用于下载尽可能少的数据量

*   `backfill_from`（默认为`None`）

    可以传递一个额外的数据源来进行初始层的回填。一旦数据源耗尽，如果需要，将从 IB 进行回填。理想情况下，这意味着要从已存储的源（如磁盘上的文件）进行回填，但不限于此。

*   `bidask`（默认为`True`）

    如果`True`，则历史/回填请求将从服务器请求出价/要价

    如果为`False`，则会请求*中点*

*   `useask`（默认为`False`）

    如果使用*bidask*价格中的*ask*部分，而不是*bid*的默认使用

*   `includeFirst`（默认为`True`）

    通过将参数直接设置到 Oanda API 调用，影响历史/回填请求的 1<sup>st</sup>条的传递

*   `reconnect`（默认为`True`）

    网络连接断开时重新连接

*   `reconnections`（默认为`-1`）

    尝试重新连接的次数：`-1`表示永远

*   `reconntimeout`（默认为`5.0`）

    重新连接尝试之间等待的时间（秒）

此数据源仅支持`timeframe`和`compression`的映射，这符合 OANDA API 开发人员 Guid 中的定义：

```py
(TimeFrame.Seconds, 5): 'S5',
(TimeFrame.Seconds, 10): 'S10',
(TimeFrame.Seconds, 15): 'S15',
(TimeFrame.Seconds, 30): 'S30',
(TimeFrame.Minutes, 1): 'M1',
(TimeFrame.Minutes, 2): 'M3',
(TimeFrame.Minutes, 3): 'M3',
(TimeFrame.Minutes, 4): 'M4',
(TimeFrame.Minutes, 5): 'M5',
(TimeFrame.Minutes, 10): 'M10',
(TimeFrame.Minutes, 15): 'M15',
(TimeFrame.Minutes, 30): 'M30',
(TimeFrame.Minutes, 60): 'H1',
(TimeFrame.Minutes, 120): 'H2',
(TimeFrame.Minutes, 180): 'H3',
(TimeFrame.Minutes, 240): 'H4',
(TimeFrame.Minutes, 360): 'H6',
(TimeFrame.Minutes, 480): 'H8',
(TimeFrame.Days, 1): 'D',
(TimeFrame.Weeks, 1): 'W',
(TimeFrame.Months, 1): 'M', 
```

任何其他组合都将被拒绝

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.5)

* calendar (None)

* historical (False)

* backfill_start (True)

* backfill (True)

* backfill_from (None)

* bidask (True)

* useask (False)

* includeFirst (True)

* reconnect (True)

* reconnections (-1)

* reconntimeout (5.0) 
```

### 潘达斯数据

将数据帧用作提要源，将索引用于列名（可以是“数字”）

这意味着与行相关的所有参数必须具有数值作为元组的索引

参数：

*   `nocase`（默认*True*）列名不区分大小写匹配

注:

*   `dataname`参数是一个数据帧

*   datetime 的可能值

    *   无：索引包含日期时间

    *   -1：无索引，自动检测列

    *   > =0 或字符串：特定列标识符

*   对于其他行参数

    *   无：列不存在

    *   -1：自动检测

    *   > =0 或字符串：特定列标识符

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* nocase (True)

* datetime (None)

* open (-1)

* high (-1)

* low (-1)

* close (-1)

* volume (-1)

* openinterest (-1) 
```

### PandasDirectData

使用数据帧作为提要源，直接迭代“itertuples”返回的元组。

这意味着与行相关的所有参数必须具有数值作为元组的索引

注:

*   `dataname`参数是一个数据帧

*   数据行的任何参数中的负值表示它不存在于它所在的数据帧中

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* datetime (0)

* open (1)

* high (2)

* low (3)

* close (4)

* volume (5)

* openinterest (6) 
```

### 昆德尔

在给定的时间范围内直接从 Quandl 服务器下载数据。

具体参数（或具体含义）：

*   `dataname`

    要下载的股票代码（例如“YHOO”）

*   `baseurl`

    服务器 url。将来可能会有人决定打开与 Quandl 兼容的服务。

*   `proxies`

    一个 dict，指示在{'http'：'[中通过哪个代理进行下载 http://myproxy.com](http://myproxy.com) }或{'http'：'[http://127.0.0.1:8080](http://127.0.0.1:8080) }

*   `buffered`

    如果为 True，则在解析开始之前，整个套接字连接将在本地进行缓冲。

*   `reverse`

    Quandl 以降序（最新的第一个）返回值。如果这是`True`（默认值），请求将告诉 Quandl 以升序（从最早到最新）格式返回

*   `adjclose`

    是否使用红利/分割调整关闭并根据其调整所有值。

*   `apikey`

    apikey 标识，以备需要

*   `dataset`

    标识要查询的数据集的字符串。默认为`WIKI`

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* headers (True)

* separator (,)

* reverse (True)

* adjclose (True)

* round (False)

* decimals (2)

* baseurl ([https://www.quandl.com/api/v3/datasets](https://www.quandl.com/api/v3/datasets))

* proxies ({})

* buffered (True)

* apikey (None)

* dataset (WIKI) 
```

### Quandlcv

解析预下载的 Quandl CSV 数据源（如果符合 Quandl 格式，则在本地生成）

具体参数：

*   `dataname`：要解析的文件名或类似文件的对象

*   `reverse`（默认为`False`）

    假设本地存储的文件在下载过程中已被反转

*   `adjclose`（默认为`True`）

    是否使用红利/分割调整关闭并根据其调整所有值。

*   `round`（默认为`False`）

    调整结束后是否将值四舍五入到特定的小数位数

*   `decimals`（默认为`2`）

    要舍入的小数位数

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* headers (True)

* separator (,)

* reverse (False)

* adjclose (True)

* round (False)

* decimals (2) 
```

### 倾翻

类，该类在满足条件时滚动到下一个将来

参数：

*   `checkdate`（默认为`None`）

    这必须是具有以下签名的*可调用*：

    ```py
    checkdate(dt, d): 
    ```

    哪里：

    *   `dt`是`datetime.datetime`对象

    *   `d`是活动未来的当前数据馈送

    预期返回值：

    *   `True`：只要可调用方返回该值，下一个将来就可能发生切换

如果商品在 3 月 3 日<sup>第</sup>周五到期，`checkdate`可能会在到期的整个一周内返回`True`。

```py
* `False`: the expiration cannot take place 
```

*   `checkcondition`（默认为`None`）

    **注意**：只有`checkdate`返回`True`时才会调用

    如果`None`这将在内部评估为`True`（执行翻滚）

    否则，这必须是具有此签名的*可调用*：

    ```py
    checkcondition(d0, d1) 
    ```

    哪里：

    *   `d0`是活动未来的当前数据馈送

    *   `d1`是下一个到期的数据馈送

    预期返回值：

    *   `True`：滚动到下一个未来

按照`checkdate`中的示例，这可以说只有当`d0`中的*音量*已经小于`d1`中的音量时，才会发生翻滚

```py
* `False`: the expiration cannot take place 
```

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* checkdate (None)

* checkcondition (None) 
```

### 锡耶拉查特斯夫达

解析[SierraChart](http://www.sierrachart.com)CSV 导出文件。

具体参数（或具体含义）：

*   `dataname`：要解析的文件名或类似文件的对象

*   使用 GenericCSVData 并简单地将日期格式（dtformat）修改为

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* headers (True)

* separator (,)

* nullvalue (nan)

* dtformat (%Y/%m/%d)

* tmformat (%H:%M:%S)

* datetime (0)

* time (-1)

* open (1)

* high (2)

* low (3)

* close (4)

* volume (5)

* openinterest (6) 
```

### VCData

VisualChart 数据馈送。

参数：

*   `qcheck`（默认值：`0.5`）用于唤醒的默认超时，以便重新采样/重放器检查当前条是否可以正常交付

    仅当在数据中插入了重采样/重放过滤器时，才使用该值

*   `historical`（默认值：`False`）如果没有提供`todate`参数（在基类中定义），这将在设置为`True`时强制历史下载

    如果提供了`todate`，则可以达到相同的效果

*   `milliseconds`（默认值：`True`）由*视觉图表*构建的条形图具有以下特征：HH:MM:59.999000

    如果此参数为`True`，则此时间将添加一毫秒，使其看起来像：HH:：MM+1:00.000000

*   `tradename`（默认值：`None`）连续期货不能交易，但非常适合数据跟踪。如果提供此参数，则它将是当前期货的名称，该期货将是交易资产。例子：

    *   001ES->ES 小型连续泵作为`dataname`供应

    *   ESU16->ES Mini 2016-09。如果在`tradename`中提供，则它将成为交易资产。

*   `usetimezones`（默认值：`True`）对于大多数市场，*视觉图表*提供的时间偏移信息允许 datetime 转换为市场时间（*backtrader*表示选择）

    有些市场是特殊的（`096`），需要特殊的内部覆盖和时区支持才能在用户预期的市场时间内显示。

    如果此参数设置为`True`导入`pytz`将尝试使用时区（默认）

    禁用它将删除时区使用（如果负载过大，可能会有所帮助）

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.5)

* calendar (None)

* historical (False)

* millisecond (True)

* tradename (None)

* usetimezones (True) 
```

### VChartCSVData

解析[VisualChart](http://www.visualchart.com)CSV 导出文件。

具体参数（或具体含义）：

*   `dataname`：要解析的文件名或类似文件的对象

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* headers (True)

* separator (,) 
```

### VChartData

支持每日和日内格式的[可视化图表](https://www.visualchart.com)二进制磁盘文件。

注:

*   `dataname`：归档或打开类文件对象

    如果传递了类似文件的对象，`timeframe`参数将用于确定哪个是实际时间段。

    否则将使用文件扩展名（`.fd`表示每日，而`.min`表示日内）。

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None) 
```

### VChartFile

支持每日和日内格式的[可视化图表](https://www.visualchart.com)二进制磁盘文件。

注:

*   `dataname`：通过视觉图表显示市场代码。示例：EuroStoxx 50 连续未来的 015ES

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None) 
```

### Yahoo FinanceCsvdata

解析预下载的 Yahoo CSV 数据源（如果符合 Yahoo 格式，则在本地生成）

具体参数：

*   `dataname`：要解析的文件名或类似文件的对象

*   `reverse`（默认为`False`）

    假设本地存储的文件在下载过程中已被反转

*   `adjclose`（默认为`True`）

    是否使用红利/分割调整关闭并根据其调整所有值。

*   `adjvolume`（默认为`True`）

    如果`adjclose`也是`True`，也要调整`volume`

*   `round`（默认为`True`）

    调整结束后是否将值四舍五入到特定的小数位数

*   `roundvolume`（默认为`0`）

    调整后，将生成的音量四舍五入到给定的小数位数

*   `decimals`（默认为`2`）

    要舍入的小数位数

*   `swapcloses`（默认为`False`）

    [2018-11-16]似乎*关闭*和*调整关闭*的顺序已经确定。保留该参数，以防再次需要交换列。

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime

* adjclose 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* headers (True)

* separator (,)

* reverse (False)

* adjclose (True)

* adjvolume (True)

* round (True)

* decimals (2)

* roundvolume (False)

* swapcloses (False) 
```

### Yahoo 金融数据

在给定的时间范围内直接从 Yahoo 服务器下载数据。

具体参数（或具体含义）：

*   `dataname`

    下载的股票代码（雅虎自有股票报价的“YHOO”）

*   `proxies`

    一个 dict，指示在{'http'：'[中通过哪个代理进行下载 http://myproxy.com](http://myproxy.com) }或{'http'：'[http://127.0.0.1:8080](http://127.0.0.1:8080) }

*   `period`

    在中下载数据的时间范围。每周通过“w”，每月通过“m”。

*   `reverse`

    [2018-11-16]雅虎在线下载的最新版本以正确的顺序返回数据。因此，在线下载的默认值`reverse`设置为`False`

*   `adjclose`

    是否使用红利/分割调整关闭并根据其调整所有值。

*   `urlhist`

    Yahoo Finance 中历史报价的 url，用于收集下载的`crumb`授权 cookie

*   `urldown`

    实际下载服务器的 url

*   `retries`

    尝试获取`crumb`cookie 并下载数据的次数（每次）

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime

* adjclose 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* headers (True)

* separator (,)

* reverse (False)

* adjclose (True)

* adjvolume (True)

* round (True)

* decimals (2)

* roundvolume (False)

* swapcloses (False)

* proxies ({})

* period (d)

* urlhist ([https://finance.yahoo.com/quote](https://finance.yahoo.com/quote)/{}/history)

* urldown ([https://query1.finance.yahoo.com/v7/finance/download](https://query1.finance.yahoo.com/v7/finance/download))

* retries (3) 
```

### Yahoolegacycv

这是为了加载在 2017 年 5 月雅虎停止原有服务之前下载的文件

线：

```py
* close

* low

* high

* open

* volume

* openinterest

* datetime

* adjclose 
```

参数：

```py
* dataname (None)

* name ()

* compression (1)

* timeframe (5)

* fromdate (None)

* todate (None)

* sessionstart (None)

* sessionend (None)

* filters ([])

* tz (None)

* tzinput (None)

* qcheck (0.0)

* calendar (None)

* headers (True)

* separator (,)

* reverse (False)

* adjclose (True)

* adjvolume (True)

* round (True)

* decimals (2)

* roundvolume (False)

* swapcloses (False)

* version () 
```