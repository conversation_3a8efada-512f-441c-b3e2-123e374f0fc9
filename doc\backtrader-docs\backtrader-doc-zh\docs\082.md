# 尺寸参考

> 原文： [https://www.backtrader.com/docu/sizers-reference/](https://www.backtrader.com/docu/sizers-reference/)

## 固定大小

#### 类 backtrader.sizers.FixedSize（）

对于任何操作，此 sizer 只返回一个固定大小。通过指定`tranches`参数，可以通过系统希望用于扩展交易的份额数量来控制规模。

参数：

```py
* `stake` (default: `1`)

* `tranches` (default: `1`) 
```

## 固定转发器

#### 类 backtrader.sizers.FixedReverser（）

此大小调整器返回需要的固定大小以反转打开位置，或返回打开位置的固定大小

*   打开一个位置：返回参数`stake`

*   要反转位置：返回 2*`stake`

参数：

```py
* `stake` (default: `1`) 
```

## 百分比测定器

#### 类 backtrader.sizers.PercentSizer（）

此 sizer 返回可用现金的百分比

参数：

```py
* `percents` (default: `20`) 
```

## AllInSizer

#### 类 backtrader.sizers.AllInSizer（）

此 sizer 返回经纪人的所有可用现金

参数：

```py
* `percents` (default: `100`) 
```

## 百分数

#### 类 backtrader.sizers.PercentSizerInt（）

此 sizer 以大小截断为整数的形式返回可用现金的百分比

参数：

```py
* `percents` (default: `20`) 
```

## AllInSizerInt

#### 类 backtrader.sizers.AllInSizerInt（）

此 sizer 返回经纪人的所有可用现金，大小被截断为 int

参数：

```py
 * `percents` (default: `100`) 
```