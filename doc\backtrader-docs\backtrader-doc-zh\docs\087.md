# 视觉图表

> 原文： [https://www.backtrader.com/docu/live/vc/vc/](https://www.backtrader.com/docu/live/vc/vc/)

与 Visual Chart 的集成支持以下两种功能：

*   *现场数据*投料

*   *现场交易*

*视觉图表*是一个完整的交易解决方案：

*   在单个平台中集成制图、数据馈送和代理

    欲了解更多信息，请访问：[www.visualchart.com](http://www.visualchart.com)

## 要求

*   *视觉图表 6*

*   *Windows*-运行 VisualChart 的一个

*   `comtypes`拨叉：[https://github.com/mementum/comtypes](https://github.com/mementum/comtypes)

    安装时使用：`pip install https://github.com/mementum/comtypes/archive/master.zip`

    *可视化图表*API 基于*COM*。

    当前`comtypes`主支路不支持`VT_RECORD`的`VT_ARRAYS`解包。这是由*视觉图表*使用的

    [拉取请求](https://github.com/enthought/comtypes/pull/104)已提交但尚未集成。一旦集成，就可以使用主分支。

*   `pytz`（可选但真正推荐）

    确保在市场时间内返回所有数据。

    大多数市场都是如此，但有些市场确实是个例外（`Global Indices`就是一个很好的例子）

    *视觉图表*中的时间管理及其与*COM*上交付时间的关系是复杂的，具有`pytz`倾向于简化事情。

## 示例代码

来源包含以下内容下的完整样本：

*   `samples/vctest/vctest.py`

该示例不能涵盖所有可能的用例，但它试图提供广泛的见解，并且应该强调，在使用回溯测试模块或实时数据模块时没有真正的区别

有一件事可以用别针指着：

*   在任何交易活动发生之前，样本等待`data.LIVE`数据状态通知。

    这可能是任何生活策略中需要考虑的问题。

## VCStore-商店

存储是实时数据馈送/交易支持的基石，在*COM*API 与数据馈送和代理需求之间提供了一层自适应。

*   使用以下方法提供获取*代理*实例的访问：

    *   `VCStore.getbroker(*args, **kwargs)`
*   提供对 getter*数据*提要实例的访问

    *   `VCStore.getedata(*args, **kwargs)`

    在这种情况下，`**kwargs`中的许多内容与`dataname`、`fromdate`、`todate`、`sessionstart`、`sessionend`、`timeframe`、`compression`等数据源相同

    该数据可提供其他参数。检查下面的参考资料。

`VCStore`将尝试：

*   使用*Windows 注册表*在系统中自动定位*VisualChart*

    *   如果找到，将扫描安装目录中的*COM*DLL，以创建*COM**typelibs*，并能够实例化适当的对象

    *   如果未找到，则将尝试使用已知和硬编码的*CLSID*执行相同操作。

笔记

即使可以通过扫描文件系统找到 DLL，*可视化图表*本身也必须运行。反向交易者不会启动*视觉图表*

`VCStore`的其他职责：

*   全面跟踪*视觉图表*与服务器的连接状态

## VCD 数据源

### 全体的

*视觉图表*提供的数据提要具有一些有趣的特性：

*   **重采样**由平台完成

    并非在所有情况下：*秒*不受支持，仍需由*反向交易者*完成

    因此，只有在用秒做某事时，最终用户才需要做：

    ```py
    vcstore = bt.stores.VCStore()
    vcstore.getdata(dataname='015ES', timeframe=bt.TimeFrame.Ticks)
    cerebro.resampledata(data, timeframe=bt.TimeFrame.Seconds, compression=5) 
    ```

    在所有其他情况下，满足以下条件即可：

    ```py
    vcstore = bt.stores.VCStore()
    data = vcstore.getdata(dataname='015ES', timeframe=bt.TimeFrame.Minutes, compression=2)
    cerebro.addata(data) 
    ```

数据将通过比较内部设备时钟和平台交付的`ticks`在内部计算`timeoffset`，以便在没有新的滴答声进入时尽早交付*自动*重采样条。

实例化数据：

*   传递*视觉图表*左上方看到的符号，不带空格。例如：

    *   *ES Mini*显示为`001 ES`。将其实例化为：

    ```py
    data = vcstore.getdata(dataname='001ES', ...) 
    ```

    *   *EuroStoxx 50*显示为`015 ES`。将其实例化为：

    ```py
    data = vcstore.getdata(dataname='015ES', ...) 
    ```

笔记

*如果名称直接从*视觉图表*粘贴，则*将努力清除位于第四位置的空白

### 时间管理

时间管理遵循*反向交易者*的一般规则

*   给出*市场*时间内的时间，以确保代码不依赖于在不同时间发生的 DST 转换，使本地时间不可靠，无法进行时间比较。

这适用于*视觉图表*中的大多数市场，但对某些市场进行了一些具体管理：

*   交易所`096`中的数据，名为`International Indices`。

    理论上，这些数据被报告在时区`Europe/London`内，但测试表明，这似乎部分是正确的，并且一些内部管理层已经到位来覆盖它。

通过传递参数`usetimezones=True`可以启用使用实时*时区*进行时间管理。如果可用，则尝试使用`pytz`。不需要，因为对于大多数市场，*视觉图表*提供的内部时间偏移允许无缝转换到市场时间。

无论如何，当`096.DJI`实际位于`US/Eastern`时，在`Europe/London`时间报告`096.DJI`似乎毫无意义。因此`backtrader`将在稍后报告。在这种情况下，`pytz`的使用超出了推荐范围。

笔记

*道琼斯工业*指数（非全球版）位于`099I-DJI`

笔记

在 DST 过渡期间，所有这些时间管理都在等待一个真正的测试，在这个过渡期间，本地和远程市场恰好与 DST 不同步。

`VCDATA`中定义了输出*时区*的`International Indices`列表：

```py
'096.FTSE': 'Europe/London',
'096.FTEU3': 'Europe/London',
'096.MIB30': 'Europe/Berlin',
'096.SSMI': 'Europe/Berlin',
'096.HSI': 'Asia/Hong_Kong',
'096.BVSP': 'America/Sao_Paulo',
'096.MERVAL': 'America/Argentina/Buenos_Aires',
'096.DJI': 'US/Eastern',
'096.IXIC': 'US/Eastern',
'096.NDX': 'US/Eastern', 
```

#### 小时间问题

使用给定的**时间**而不是默认的`00:00:00`传递`fromdate`或`todate`似乎会在*COM*API 中创建一个过滤器，任何一天的条形图都只会在给定的时间之后交付。

像这样的：

*   请仅将**完整日期**传递给`VCData`，如下所示：

    ```py
    data = vcstore.getdata(dataname='001ES', fromdate=datetime(2016, 5, 15)) 
    ```

    而不是：

    ```py
    data = vcstore.getdata(dataname=‘001ES’, fromdate=datetime(2016, 5, 15, 8, 30)) 
    ```

#### 回填时间长度

如果最终用户未指定`fromdate`，平台将自动尝试使用实时数据进行回填和携带。回填取决于时间范围，并且：

*   `Ticks`、`MicroSeconds`、`Seconds`：**1 天**

    考虑到*视觉图表*不直接支持*秒*和*微秒*，并通过*节拍*的重采样完成，3 个时间段的情况也是如此

*   `Minutes`：**2 天**

*   `Days`：**1 年**

*   `Weeks`：**2 年**

*   `Months`：**5 年**

*   `Months`：**20 年**

定义的回填周期乘以请求的`compression`，即：如果*时间段*为`Minutes`，且*压缩*为 5，则最终*回填周期*为：`2 days * 5 -> 10 days`

### 交易数据

*视觉图表*提供**连续期货**。无需手动管理，您可以不间断地跟踪选择的未来。这是一个优势，也是一个小挑战：

*   `ES-Mini`为`001ES`，但实际交易资产（例如：2016 年 9 月）为`ESU16`。

为了克服这一点，允许策略跟踪*连续未来*并在*真实资产*上交易，可以在数据实例化期间指定以下内容：

```py
data = vcstore.getdata(dataname='001ES', tradename='ESU16') 
```

交易将在`ESU16`发生，但数据馈送将为 frm`001ES`（数据相同，持续 3 个月）

### 其他参数

*   `qcheck`（默认值：`0.5`秒）控制唤醒与内部重采样器/重放器通话的频率，以避免酒吧延迟交付。

    以下逻辑将应用于此参数：

    *   如果检测到内部*重采样/重放*，则该值将按原样使用。

    *   如果没有检测到内部*重采样/重放*，数据馈送将不会唤醒，因为没有要报告的内容。

    数据馈送仍将唤醒以检查*视觉图表*内置重采样器，但这是自动控制的。

### 数据通知

数据馈送将通过以下一项或多项报告当前状态（检查*大脑*和*策略*参考）

*   `Cerebro.notify_data`（如果被覆盖）n

*   添加了`Cerebro.adddatacb`的回调

*   `Strategy.notify_data`（如果被覆盖）

*策略*中的一个例子：

```py
class VCStrategy(bt.Strategy):

    def notify_data(self, data, status, *args, **kwargs):

        if status == data.LIVE:  # the data has switched to live data
           # do something
           pass 
```

系统中的以下更改将发送以下通知：

*   `CONNECTED`

    初始连接成功时发送

*   `DISCONNECTED`

    在这种情况下，无法再检索数据，数据将指示系统无法执行任何操作。可能的条件：

    *   指定了错误的合同

    *   历史下载期间的中断

    *   已超过尝试重新连接到 TWS 的次数

*   `CONNBROKEN`

    TWS 或数据场的连接已丢失。数据馈送将尝试（通过存储）在需要时重新连接和回填，并恢复操作

*   `NOTSUBSCRIBED`

    协定和连接正常，但由于缺少权限，无法检索数据。

    数据将向系统指示它无法检索数据

*   `DELAYED`

    信号表示*历史*/*回填*操作正在进行，策略处理的数据不是实时数据

*   `LIVE`

    表示*策略*从此点开始处理的数据为实时数据

Apple T0 策略的开发者应该考虑在断开发生时或当接收到 Ty2 T2 延迟的 T3 数据时采取哪些行动。

## VCBroker-实时交易

### 使用代理

要使用*VCBroker*，必须替换*大脑*创建的标准 broker 模拟实例。

使用*商店*型号（首选）：

```py
import backtrader as bt

cerebro = bt.Cerebro()
vcstore = bt.stores.VCStore()
cerebro.broker = vcstore.getbroker()  # or cerebro.setbroker(...) 
```

### 代理参数

无论是直接还是通过`getbroker`代理`VCBroker`都不支持任何参数。这是因为经纪人只是真实的*经纪人*的代理。而真正的经纪人所给予的，不应被剥夺。

### 限制

#### 位置

*视觉图表*报告**打开位置**。这可以在大部分时间用于控制实际位置，但缺少指示*位置*已关闭的最终事件。

这使得*反向交易者*必须对*头寸*进行完整核算，并与您账户中以前存在的任何头寸分开

#### 委员会

*COM*交易界面不报告佣金。*反向交易者*没有机会做出有根据的猜测，除非：

*   *经纪人*被实例化为一个*佣金*实例，指示实际发生的佣金。

### 用它交易

#### 账户

*视觉图表*支持一个经纪人同时拥有多个账户。所选科目可以通过以下参数进行控制：

*   `account`（默认为`None`）

    VisualChart 在代理上同时支持多个帐户。如果默认`None`已到位，则将使用 ComTrader`Accounts`托收中的 1<sup>st</sup>账户。

    如果提供了帐户名，则会检查并使用`Accounts`集合（如果存在）

#### 异议

标准使用方面没有变化。只需使用策略中可用的方法（完整解释请参见`Strategy`参考）

*   `buy`

*   `sell`

*   `close`

*   `cancel`

### 返回的订单对象

*   标准*反向交易者*`Order`对象

### 订单执行类型

*视觉图表*支持*反向交易者*所需的最低订单执行类型，因此，反向测试的任何东西都可以上线。

因此，订单执行类型仅限于*经纪人模拟*中可用的类型：

*   `Order.Market`

*   `Order.Close`

*   `Order.Limit`

*   `Order.Stop`（触发*停止*时，跟随*市场*指令）

*   `Order.StopLimit`（触发*停止*时，跟随*限制*指令）

### 订单有效期

回溯测试中可用的相同有效性概念（具有`valid`至`buy`和`sell`的有效性概念）可用且具有相同的含义。因此，`valid`参数对于*视觉图表订单*转换为以下值：

*   `None`翻译为*良好，直到取消*

    由于未指定有效期，因此订单必须在取消前有效

*   `datetime/date`翻译为*良好截止日期*

    笔记

    注意：*视觉图表*只支持“完整日期”*时间*部分被丢弃。

*   `timedelta(x)`翻译为*截止日期*（此处为`timedelta(x) != timedelta()`）

    笔记

    注意：*视觉图表*只支持**完整日期**且*时间*部分被丢弃。

    这被解释为命令从`now`+`timedelta(x)`开始生效的信号

*   `timedelta() or 0`翻译为*会话*

    已传递一个值（而不是`None`），但该值为*空*，并被解释为对当前*天*（会话）有效的订单

### 通知

标准`Order`状态将通过`notify_order`方法通知*策略*（如果被覆盖）

*   `Submitted`-订单已发送至 TWS

*   `Accepted`-已下订单

*   `Rejected`-下单失败或在其有效期内被系统取消

*   `Partial`-部分执行已发生

*   `Completed`-订单已全部执行

*   `Canceled`（或`Cancelled`）

*   `Expired`-目前尚未报告。需要一种启发式方法来区分此状态与`Cancelled`

## 参考

### VCStore

#### 类 backtrader.stores.VCStore（）

包装 ibpy ibConnection 实例的 Singleton 类。

也可以在使用此存储的类中指定参数，如`VCData`和`VCBroker`

### VCBroker

#### 类 backtrader.brokers.VCBroker（**kwargs）

VisualChart 的代理实现。

此类将订单/位置从 VisualChart 映射到`backtrader`的内部 API。

参数：

*   `account`（默认为无）

    VisualChart 在代理上同时支持多个帐户。如果默认`None`已到位，则将使用 ComTrader`Accounts`托收中的 1<sup>st</sup>账户。

    如果提供了帐户名，则会检查并使用`Accounts`集合（如果存在）

*   `commission`（默认为无）

    如果未将佣金方案作为参数传递，则将自动生成对象

    请参阅下面的注释以了解更多说明

### 笔记

*   位置

VisualChart 通过 ComTrader 界面报告“未平仓”更新，但仅当仓位有“大小”时。如果没有某个位置，则会报告该位置已移到零的更新。这迫使我们通过查看执行事件来记录头寸，就像模拟代理一样

*   委员会

VisualChart 的 ComTrader 界面不报告佣金，因此自动生成的 CommissionInfo 对象无法使用不存在的佣金来正确计算佣金。为了支持佣金，`commission`参数必须与适当的佣金方案一起传递。

有关佣金计划的文件详细说明了如何做到这一点

*   到期时间

ComTrader 接口（或者是 comtypes 模块？）从`datetime`对象中丢弃`time`信息，到期日期始终为完整日期。

*   到期报告

目前还没有启发式方法来确定取消的订单何时因到期而取消。因此，过期订单将被报告为已取消。

### VCData

#### 类 backtrader.feeds.VCData（**kwargs）

VisualChart 数据馈送。

参数：

*   `qcheck`（默认值：`0.5`）用于唤醒的默认超时，以便重新采样/重放器检查当前条是否可以正常交付

    仅当在数据中插入了重采样/重放过滤器时，才使用该值

*   `historical`（默认值：`False`）如果没有提供`todate`参数（在基类中定义），这将在设置为`True`时强制历史下载

    如果提供了`todate`，则可以达到相同的效果

*   `milliseconds`（默认值：`True`）由*视觉图表*构建的条形图具有以下特征：HH:MM:59.999000

    如果此参数为`True`，则此时间将添加一毫秒，使其看起来像：HH:：MM+1:00.000000

*   `tradename`（默认值：`None`）连续期货不能交易，但非常适合数据跟踪。如果提供此参数，则它将是当前期货的名称，该期货将是交易资产。例子：

    *   001ES->ES 小型连续泵作为`dataname`供应

    *   ESU16->ES Mini 2016-09。如果在`tradename`中提供，则它将成为交易资产。

*   `usetimezones`（默认值：`True`）对于大多数市场，*视觉图表*提供的时间偏移信息允许 datetime 转换为市场时间（*backtrader*表示选择）

    有些市场是特殊的（`096`），需要特殊的内部覆盖和时区支持才能在用户预期的市场时间内显示。

    如果此参数设置为`True`导入`pytz`将尝试使用时区（默认）

    禁用它将删除时区使用（如果负载过大，可能会有所帮助）