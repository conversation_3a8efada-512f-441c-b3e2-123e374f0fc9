# 命令

> 原文： [https://www.backtrader.com/docu/order/](https://www.backtrader.com/docu/order/)

`Cerebro`是`backtrader`中的关键控制系统，`Strategy`（一个子类）是最终用户的关键控制点。后者需要一种链接到系统其他部分的方法，这就是**订单**起关键作用的地方。

*命令*将`Strategy`中的逻辑所做的决定转化为适合`Broker`执行动作的消息。这是通过以下方式完成的：

*   *创作*

    通过策略的方法：`buy\``,`卖出`and`成交`(Strategy) which return an`订单`实例作为参考

*   *取消*

    通过策略的方法：`cancel`（策略）取订单实例进行操作

而*订单*也作为一种通信方式返回给用户，通知代理中的事情是如何运行的。

*   *通知*

    至策略方法：`notify_order`（策略），报告`order`实例

## 订单创建

调用`buy`、`sell`和`close`时，以下参数适用于创建：

*   `data`（默认为`None`）

    必须为其创建订单的数据。如果为`None`，则将使用系统中的第一个数据`self.datas[0] or self.data0`（又名`self.data`

*   `size`（默认为`None`）

    要用于订单的数据单位的要使用的大小（正）。

    如果`None`将使用`getsizer`检索的`sizer`实例来确定大小。

*   `price`（默认为`None`）

    使用价格（如果实际格式不符合最小刻度大小要求，实时经纪人可能会对其进行限制）

    `None`对`Market`和`Close`订单有效（价格由市场决定）

    对于`Limit`、`Stop`和`StopLimit`订单，该值确定触发点（在`Limit`的情况下，触发点显然是订单应匹配的价格）

*   `plimit`（默认为`None`）

    仅适用于`StopLimit`订单。这是在触发*停止*后（已使用`price`时）设置隐含*限制*订单的价格

*   `exectype`（默认为`None`）

    可能值：

    *   `Order.Market`或`None`。市场订单将以下一个可用价格执行。在回溯测试中，它将是下一个酒吧的开盘价

    *   `Order.Limit`。只能在给定的`price`或更好的条件下执行的命令

    *   `Order.Stop`。在`price`触发并像`Order.Market`指令一样执行的指令

    *   `Order.StopLimit`。在`price`触发并作为隐含*限额*订单执行的订单，价格由`pricelimit`给出

*   `valid`（默认为`None`）

    可能值：

    *   `None`：这将生成一个不会过期的订单（也称为*在取消之前有效*），并在匹配或取消之前保持在市场中。在现实中，经纪人倾向于施加时间限制，但通常情况下，在时间上远不及考虑到期。

    *   `datetime.datetime`或`datetime.date`实例：该日期将用于生成有效期至给定日期时间的订单（又名*有效期至日期*）

    *   `Order.DAY`或`0`或`timedelta()`：生成有效期至会话*结束的一天（又名*天*订单）*

    *   `numeric value`：假设该值对应于`matplotlib`编码中的日期时间（由`backtrader`使用的日期时间），并将用于生成在此时间之前有效的订单（*截止日期*）

*   `tradeid`（默认为`0`）

    这是`backtrader`应用的内部值，用于跟踪同一资产上的重叠交易。此`tradeid`在通知订单状态变更时被发送回*策略*。

*   `**kwargs`：额外的代理实现可能支持额外的参数。`backtrader`将*kwargs*传递给已创建的订单对象

    示例：如果`backtrader`直接支持的 4 种订单执行类型不够，例如*交互经纪人*的情况下，以下可以传递为*kwargs*：

    ```py
    orderType='LIT', lmtPrice=10.0, auxPrice=9.8 
    ```

    这将覆盖`backtrader`创建的设置并生成`LIMIT IF TOUCHED`订单，其中*触摸*价格为 9.8，*限制*价格为 10.0。

笔记

`close`方法将检查当前位置，并相应地使用`buy`或`sell`来有效**关闭**位置。除非参数是用户输入，否则也会自动计算`size`，在这种情况下，可以实现部分*关闭*或*反转*

## 订单通知

要接收通知，必须在用户子类`Strategy`中重写`notify_order`方法（默认行为是什么都不做）。以下内容适用于这些通知：

*   在调用策略的`next`方法之前发出

*   同一*订单*在同一*下一*周期内，状态相同或不同的*订单可能（也将）发生多次。*

    *指令*可以提交给*经纪人*并在`next`再次调用之前*接受*并完成*执行*。

    在这种情况下，至少会发生 3 次通知，通知值如下`status`：

    *   `Order.Submitted`因为订单已经发送给*经纪人*

    *   `Order.Accepted`因为该订单由*经纪人*执行，等待潜在执行

    *   `Order.Completed`因为在本例中，它是快速匹配和完全填充的（这可能是`Market`订单通常的情况）

在`Order.Partial`的情况下，对于相同的状态，通知甚至可能发生多次。这种状态将不会出现在 Oracle T1 后退测试 Ty2 T2 代理中（它不考虑匹配时的卷），但它肯定会由真正的经纪人设置。

真实经纪人可能会在更新头寸之前发出一次或多次执行，这组执行将弥补`Order.Partial`通知。

实际执行数据在属性`order.executed`中，该属性为`OrderData`类型的对象（参考如下），常用字段为`size`和`price`

创建时的值存储在`order.created`中，该值在`order`的整个生命周期内保持不变

## 订单状态值

定义如下：

*   `Order.Created`：创建`Order`实例时设置。除非`order`实例是手动创建的，而不是通过`buy`、`sell`和`close`创建的，否则最终用户永远不会看到

*   `Order.Submitted`：当`order`实例传输到`broker`时设置。这仅仅意味着它已经被*发送*。在*回溯测试*模式下，这将是一项即时行动，但可能需要实际的*时间*与真正的经纪人，经纪人可能会收到订单，并且只有在订单被转发到交易所时才会首先通知

*   `Order.Accepted`：`broker`已接受订单，并根据设置的执行类型、大小、价格、有效期等参数在系统中（或已在交易所中）等待执行

*   `Order.Partial`：`order`已部分执行。`order.executed`包含当前填写的`size`和平均价格。

    `order.executed.exbits`包含一份完整的`ExecutionBits`清单，详细说明了部分填充物

*   `Order.Complete`：`order`已全部填入均价。

*   `Order.Rejected`：`broker`已拒绝订单。`broker`可能不接受参数（例如用于确定其寿命的`valid`，而`order`则不能接受。

    原因将通过`strategy`的`notify_store`方式通知。虽然这看起来很尴尬，但原因是现实生活中的经纪人会在一个事件中通知这一点，这可能与订单直接相关，也可能与订单无关。但经纪人的通知仍然可以在`notify_store`中看到。

    在*回溯测试*代理中不会看到此状态

*   `Order.Margin`：订单执行意味着追加保证金，之前接受的订单已从系统中删除

*   `Order.Cancelled`（或`Order.Canceled`：用户请求取消的确认

    必须考虑到，通过策略的`cancel`方法*取消*订单的请求并不能保证取消。订单可能已经执行，但经纪人可能尚未通知此类执行和/或通知可能尚未送达策略

*   `Order.Expired`：之前接受的具有时间有效性的*订单*已经过期并从系统中删除

## 参考：订单和相关类

这些对象是`backtrader`生态系统中的泛型类。当与其他经纪人合作时，它们可能会被扩展和/或包含额外的嵌入信息。请参阅相应经纪人的参考资料

#### 类 backtrader.order.order（）

类，该类保存创建/执行数据和订单类型。

订单可能具有以下状态：

*   已提交：发送给经纪人并等待确认

*   已接受：经纪人已接受

*   部分：部分执行

*   已完成：完全兴奋

*   取消/取消：由用户取消

*   过期：过期

*   保证金：没有足够的现金执行订单。

*   拒绝：被经纪人拒绝

    这可能发生在订单提交期间（因此订单不会达到接受状态）或在执行每个新的酒吧价格之前，因为现金已由其他来源提取（类似未来的工具可能减少了现金或订单已执行）

成员属性：

*   ref：唯一订单标识符

*   已创建：保存创建数据的 OrderData

*   已执行：保存执行数据的 OrderData

*   信息：通过`addinfo()`方式传递的自定义信息。它以已子类化的 OrderedDict 的形式保存，因此也可以使用“.”符号指定键

用户方法：

*   isbuy（）：返回 bool，指示订单是否购买

*   issell（）：返回 bool，指示订单是否销售

*   alive（）：如果订单处于部分或接受状态，则返回 bool

#### 类 backtrader.order.OrderData（dt=None，size=0，price=0.0，pricelimit=0.0，remsize=0，pclose=0.0，trailamount=0.0，trailpercent=0.0）

保存用于创建和执行的实际订单数据。

在创建的情况下，提出的请求，在执行的情况下，实际结果。

成员属性：

*   exbits：此 OrderData 的 OrderExecutionBits 的 iterable

*   dt:datetime（浮点）创建/执行时间

*   大小：请求/执行的大小

*   价格：执行价格注：如果未给出价格且未给出价格限制，则将使用创建订单时的收盘价作为参考

*   pricelimit：保留 StopLimit 的 pricelimit（首先有触发器）

*   trailamount：跟踪站点的绝对价格距离

*   trailpercent：跟踪站点的价格距离百分比

*   价值：整个钻头尺寸的市场价值

*   comm：整个位执行的佣金

*   pnl：由该位生成的 pnl（如果某个对象已关闭）

*   保证金：订单产生的保证金（如有）

*   psize：当前打开的位置大小

*   pprice：当前未平仓价格

#### 类 backtrader.order.OrderExecutionBit（dt=None，size=0，price=0.0，closed=0，closedvalue=0.0，closedCommand=0.0，openedvalue=0.0，openedCommand=0.0，pnl=0.0，psize=0，pprice=0.0）

用于保存有关订单执行的信息。“位”不确定订单是否已完全/部分执行，它只保存信息。

成员属性：

*   dt:datetime（浮点）执行时间

*   大小：执行了多少

*   价格：执行价格

*   关闭：有多少执行关闭了现有职位

*   打开：有多少执行打开了一个新的位置

*   开放价值：“开放”部分的市场价值

*   封闭价值：“封闭”部分的市场价值

*   closedcomm：“已关闭”零件的佣金

*   openedcomm：“打开”零件的佣金

*   价值：整个钻头尺寸的市场价值

*   comm：整个位执行的佣金

*   pnl：由该位生成的 pnl（如果某个对象已关闭）

*   psize：当前打开的位置大小

*   pprice：当前未平仓价格