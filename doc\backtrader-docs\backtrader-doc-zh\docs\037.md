# 雅虎数据源注释

> 原文： [https://www.backtrader.com/docu/datayahoo/](https://www.backtrader.com/docu/datayahoo/)

2017 年 5 月，雅虎停止使用现有 API 下载*csv*格式的历史数据。

一个新的 API（此处命名为`v7`）很快*标准化*并已实现。

这也改变了实际的 CSV 下载格式。

## 使用 V7API/格式

从版本`1.9.49.116`开始，这是默认行为。选择

*   `YahooFinanceData`用于在线下载

*   `YahooFinanceCSVData`对于离线下载的文件

## 使用遗留 API/格式

使用旧的 API/格式

1.  将在线 Yahoo 数据源实例化为：

    ```py
    data = bt.feeds.YahooFinanceData(
        ...
        version='',
        ...
    ) 
    ```

    脱机 Yahoo 数据源的名称为：

    ```py
    data = bt.feeds.YahooFinanceCSVData(
        ...
        version='',
        ...
    ) 
    ```

    可能是在线服务回来了（服务*在没有任何通知的情况下*停止了……也可能回来了）

或

1.  仅对于更改发生前下载的脱机文件，还可以执行以下操作：

    ```py
    data = bt.feeds.YahooLegacyCSV(
        ...
        ...
    ) 
    ```

    新的`YahooLegacyCSV`只是使用`version=''`实现自动化