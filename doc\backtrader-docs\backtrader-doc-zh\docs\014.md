# 安装

> 原文： [https://www.backtrader.com/docu/installation/](https://www.backtrader.com/docu/installation/)

## 要求和版本

`backtrader`是自包含的，没有外部依赖关系（除非您想要绘图）

基本要求是：

*   Python 2.7

*   Python 3.2/3.3/3.4/3.5

*   pypy/pypy3

如果希望打印，则需要附加要求：

*   Matplotlib>=1.4.1

    它可能适用于以前的版本，但这是用于开发的版本

**注**：*pypy/pypy3*下不支持 Matplotlib 编写时

### Python 2.x/3.x 兼容性

开发在 Python2.7 下进行，有时在 3.4 下进行。两个版本的测试都在本地运行。

与 3.2/3.3/3.5 和 pypy/pyp3 的兼容性在 Travis 下通过持续集成进行检查

## 从 pypi 安装

例如，使用 pip：

```py
pip install backtrader 
```

也可以应用相同语法的*简易安装*

## 从 pypi 安装（包括*matplotlib*）

如果需要打印功能，请使用此选项：

```py
pip install backtrader[plotting] 
```

这会拉入 matplotlib，而 matplotlib 又会拉入其他依赖项。

同样，您可能更喜欢（或只能访问…）`easy_install`

## 从源代码安装

首先从 github 站点下载版本或最新的 tarball：

*   [https://github.com/mementum/backtrader](https://github.com/mementum/backtrader)

解包后，运行以下命令：

```py
python setup.py install 
```

## 从项目中的源代码运行

从 github 站点下载版本或最新的 tarball：

*   [https://github.com/mementum/backtrader](https://github.com/mementum/backtrader)

然后将*backtrader*包目录复制到您自己的项目中。例如，在类 Unix 操作系统下：

```py
tar xzf backtrader.tgz
cd backtrader
cp -r backtrader project_directory 
```

记住，然后需要手动安装`matplotlib`进行绘图。