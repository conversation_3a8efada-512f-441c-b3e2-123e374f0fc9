# 贸易

> 原文： [https://www.backtrader.com/docu/trade/](https://www.backtrader.com/docu/trade/)

贸易的定义：

*   当工具中的 A 仓位从 0 变为大小 X（多头/空头仓位可能为正/负）时，交易开放）

*   当头寸从 X 变为 0 时，交易结束。

以下两项行动：

*   从正到负

*   从负到正

实际上被视为：

1.  交易已结束（头寸从 X 变为 0）

2.  新交易已开始（头寸从 0 变为 Y）

交易只提供信息，没有用户可调用的方法。

## 参考资料：贸易

#### 类 backtrader.trade.trade（data=None，tradeid=0，historyon=False，size=0，price=0.0，value=0.0，commission=0.0）

跟踪交易的生命周期：规模、价格、佣金（和价值？）

从 0 开始的交易可以增加或减少，如果回到 0，则视为成交。

交易可以是多头（正规模）或空头（负规模）

交易不意味着被逆转（逻辑上没有支持）

成员属性：

*   `ref`：唯一贸易标识符

*   `status`（`int`：已创建、打开、关闭其中一个

*   `tradeid`：创建订单时传递给订单的分组 tradeid 订单中默认为 0

*   `size`（`int`：当前交易规模

*   `price`（`float`：交易的当前价格

*   `value`（`float`：该笔交易的现值

*   `commission`（`float`：本次累计佣金

*   `pnl`（`float`：交易当期损益（pnl 总额）

*   `pnlcomm`（`float`：交易当期损益减去佣金（净 pnl）

*   `isclosed`（`bool`：记录上次更新是否关闭（将大小设置为空交易）

*   `isopen`（`bool`：记录是否有更新开启交易

*   `justopened`（`bool`）：如果交易刚刚开始

*   `baropen`（`int`）：该交易开张的酒吧

*   `dtopen`（`float`：开盘交易的浮点数编码日期时间

    *   使用方法`open_datetime`获取 Python datetime.datetime 或使用平台提供的`num2date`方法
*   `barclose`（`int`：该交易关闭的酒吧

*   `dtclose`（`float`：交易结束的浮动编码日期时间

    *   使用方法`close_datetime`获取 Python datetime.datetime 或使用平台提供的`num2date`方法
*   `barlen`（`int`：本次交易开放的酒吧数量

*   `historyon`（`bool`：是否需要记录历史

*   `history`（`list`）：保存随每个“更新”事件更新的列表，其中包含更新中使用的结果状态和参数

    历史记录中的第一个条目是开始事件，历史记录中的最后一个条目是结束事件