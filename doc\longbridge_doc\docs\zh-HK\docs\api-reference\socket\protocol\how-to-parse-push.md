---
title: 解析推送包
id: how-to-parse-push
slug: /socket/protocol/push
sidebar_position: 6
---

推送用於一端向另一端發送數據，接收方不需要進行回覆

:::info
當包頭中的 `type` 值爲 `3` 時，數據包爲推送包
:::

```
 0                   1                   2                   3
 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
| type=3|v|g|re.|    cmd_code   |            body_len           |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|  body_len     |               body(by body_len)               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                                                               |
+                        nonce(optional)                        +
|                                                               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                                                               |
+                                                               +
|                                                               |
+                      signature(optional)                      +
|                                                               |
+                                                               +
|                                                               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
```

字段說明：

| 字段      | 長度 (bit)                 | 長度（字節）| 說明                                                                                 |
| --------- | -------------------------- | ------------ | ------------------------------------------------------------------------------------ |
| cmd_code  | 8                          | 1            | 指令 cmd 值                                                                          |
| body_len  | 24(uint32)                 | 3            | `body` 長度，單位：字節，最大 16 MB 數據；如果 gzip 爲 1，該值爲 `body` 壓縮後的長度 |
| body      | 可變長度，由 body_len 決定 | 可變長度     | `body`，最大 16 MB                                                                   |
| nonce     | 64                         | 8            | 僅當包頭中的 `verify` 爲 1 時存在                                                    |
| signature | 128                        | 16           | 僅當包頭中的 `verify` 爲 1 時存在                                                    |
