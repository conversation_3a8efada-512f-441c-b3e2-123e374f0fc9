---
title: 解析响应包
id: how-to-parse-response
slug: /socket/protocol/response
sidebar_position: 5
---

当服务端收到客户端的请求包后必须响应一个响应包回来

:::info
当包头中的 `type` 值为 `2` 时，数据包为请求包
:::

## 结构

```
 0                   1                   2                   3
 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
| type=2|v|g|re.|    cmd_code   |           request_id          |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                               |  status_code  |    body_len   |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|            body_len           |                               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+                               +
|                       body(by body_len)                       |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                                                               |
+                        nonce(optional)                        +
|                                                               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                                                               |
+                                                               +
|                                                               |
+                      signature(optional)                      +
|                                                               |
+                                                               +
|                                                               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
```

字段说明：

| 字段       | 长度 (bit)                 | 长度（字节）| 说明                                                                               |
| ---------- | -------------------------- | ------------ | ---------------------------------------------------------------------------------- |
| cmd_code   | 8                          | 1            | 指令 cmd 值                                                                        |
| request_id | 32(uint32)                 | 4            | 请求 id，同一个连接的 id 需要唯一，从 1 开始，到达 4294967295 后从新开始。         |
| status     | 8(uint8)                   | 1            | 状态码 `0` - 成功；参考状态码表                                                    |
| body_len   | 24(uint32)                 | 3            | `body` 长度，单位：字节，最大 16 MB 数据；如果 gzip 为 1，该值为 body 压缩后的长度 |
| body       | 可变长度，由 body_len 决定 | 可变长度     | `body`，最大 16 MB                                                                 |
| nonce      | 64                         | 8            | 仅当包头中的 `verify` 为 1 时存在                                                  |
| signature  | 128                        | 16           | 仅当包头中的 verify 为 1 时存在                                                    |

## 响应包状态码

响应包有状态说明：

| 值  | 标识                  | 说明                                      |
| --- | --------------------- | ----------------------------------------- |
| 0   | SUCCESS               | 成功，类似于 HTTP 200                     |
| 1   | SERVER_TIMEOUT        | 服务端超时，类似于 HTTP 408               |
| 3   | BAD_REQUEST           | 请求错误，通常为参数错误，类似于 HTTP 400 |
| 5   | UNAUTHENTICATED       | 鉴权失败，类似于 HTTP 401                 |
| 7   | SERVER_INTERNAL_ERROR | 服务端内部错误，类似于 HTTP 500           |
