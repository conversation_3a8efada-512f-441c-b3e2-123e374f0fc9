# 工作机会

> 原文： [https://www.backtrader.com/home/<USER>/jobs/](https://www.backtrader.com/home/<USER>/jobs/)

### 羊驼

*   [AngelCo——羊驼的开源开发者](https://angel.co/company/alpaca/jobs/398197-open-source-developer)

 *### 代码导师

*   [Python 专家，具备构建自动交易机器人（远程）&fa 外部链接](https://www.codementor.io/remote-jobs/details/6ihemotrf8)所需的反向交易者经验

### 特约人员

*   [Python（backtrader/zipline）和互动经纪人&fa 外部链接](https://www.freelancer.com/projects/python/python-backtrader-zipline-interactive/)

*   [编译 Python 程序（BackTrader 和 Interactive Brokers API）并建立用户界面网站](https://www.freelancer.com/projects/php/compile-python-programs-backtrader/)

**   [Python 回溯测试平台](https://www.freelancer.com/projects/python/python-backtesting-platform/)

    **   [詹姆斯 A 项目-2](https://www.freelancer.com/projects/java/project-for-james-17134165/)

    **   [金融市场交易框架](https://www.freelancer.com/projects/software-architecture/framework-for-trading-financial-markets/)

    **   [赛拉一期工程](https://www.freelancer.com/projects/php/project-for-saira-16611437/)

    **   [Tensorflow](https://www.freelancer.com/projects/software-architecture/tensorflow/)

    **   [算法交易 Python 平台](https://www.freelancer.com/projects/python/algorithmic-trading-python-platform/)

    **   [私人项目或竞赛#15215084](https://www.freelancer.com/login?goto=YzdjZjYxODdkNDY2NGZiZWZhYWQ5YzlmOWNiOWU5NTFodHRwczovL3d3dy5mcmVlbGFuY2VyLmNvbS9wcm9qZWN0cy9waHAvcHJvamVjdC1mb3ItbWFyY2Vsby0xNTIxNTA4NC8,)

    **   [需要具备 backtrader（算法交易）知识的自由职业者](https://www.freelancer.co.za/projects/algorithm/need-freelancer-with-knowledge/#)******** 

 ***### 佛罗里达州

*   [Python-创建交易策略测试平台](https://www.fl.ru/projects/4084063/python---sozdanie-platformyi-dlya-testirovaniya-torgovyih-strategiy.html)

 *### Guru.com

*   [反向交易者分析仪](https://www.guru.com/jobs/backtrader-analyzers/1620746&SearchUrl=search.aspx?)

 ***语言：俄语**

### 好来网络科技（上海）有限公司。

*   [定量工程师](https://cn.linkedin.com/jobs/view/%E9%87%8F%E5%8C%96%E5%B7%A5%E7%A8%8B%E5%B8%88-at-%E6%98%A7%E6%9D%A5%E7%BD%91%E7%BB%9C%E7%A7%91%E6%8A%80%EF%BC%88%E4%B8%8A%E6%B5%B7%EF%BC%89%E6%9C%89%E9%99%90%E5%85%AC%E5%8F%B8-1035267305)

 ***语言：中文**

### 思想库

*   [数据分析员](https://www.naukri.com/job-listings-Data-Analyst-Mindpool-Technologies-Pvt-Ltd-Mumbai-4-to-6-years-101018500874?src=seo_srp&sid=15424766186813&xp=2&px=1)

 *### MQL5

*   [将 MT4 指标转换为 Python（反向交易者）](https://www.mql5.com/en/job/110781)

**   [MQL5 对 Python 的测试](https://www.mql5.com/en/job/110670)

    **   [将 MQL 性能度量转换为 python-](https://www.mql5.com/en/job/111750)** 

 ***### 人脉

*   [Backtrader 实时数据集成-python](https://www.peopleperhour.com/freelance-jobs/software-development/integration-api/backtrader-realtime-data-integration-python-2003107)

 *### 昆坦斯蒂

*   [LinkedIn 样本](https://in.linkedin.com/jobs/view/python-developer-data-structure-algorithm-%282-5-yrs%29-mumbai-at-quantinsti-*********)*{:target=_blank}*

**   [AngelCo——Quantinsti 定量学习](https://angel.co/company/quantinsti-quantitative-learning/jobs/306165-python-developer)*的 Python 开发人员*

    **   [智慧乔布斯-Python 开发者](https://www.wisdomjobs.com/jobs/python-developer-jobs-in-mumbai-quantinsti-quantitative-learning-pvt-ltd-openings-21960506)

    **   [时代作业-Python 开发者](https://www.timesjobs.com/job-detail/it-software-application-programming-job-in-quantinsti-quantitative-learning-pvt-ltd-mumbai-jobid-k5yp4SeZ5pVzpSvf__PLUS__uAgZw==)*** 

 ***### 李白

*   [量化回溯测试系统](https://shixian.com/jobs/1981651029)

     ***注**中文。* 

 *### 上升运动

*   [自由职业者上市*【反向交易者】*](https://www.upwork.com/o/profiles/browse/?q=backtrader)

**   [加密货币交易机器人界面](https://www.upwork.com/jobs/Cryptocurrency-Trading-Bot_~01b7f9750af33d2fc4?source=rss)

    **   [加密货币交易机器人界面](https://www.upwork.com/jobs/Cryptocurrency-Trading-Bot_~01b7f9750af33d2fc4?source=rss)

    **   [祖鲁泉](https://www.upwork.com/mobile/jobs/_~0126fca498fbb93d7b)

     *（这里还有：[社区-祖鲁泉-合同岗位](https://community.backtrader.com/topic/546/contract-position)* ***   [反向交易辅助。轻松快速现金作业](https://www.upwork.com/job/BackTrader-Assistances-Easy-Quick-Fast-Cash-Job_~01a17105602dd59412/)

    **   [在 backtrader 和 python 中开发交易算法](https://www.upwork.com/job/Development-trading-algorithms-backtrader-and-python_~01df00f98eb93dce83/)****** 

 ***### 智慧工作

*   [Python 架构师-数据结构/算法](https://www.wisdomjobs.com/jobs/python-architect-data-structure-a-jobs-in-mumbai-quantinsti-quantitative-learning-pvt-ltd-openings-6356247)*******************