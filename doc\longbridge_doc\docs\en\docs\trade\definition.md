---
id: trade_definition
title: Definition
slug: trade-definition
sidebar_position: 2
---

## OrderType

- Description: HongKong stock support order type

| Enum    | Description                                     |
| ------- | ----------------------------------------------- |
| LO      | Limit Order                                     |
| ELO     | Enhanced Limit Order                            |
| MO      | Market Order                                    |
| AO      | At-auction Order                                |
| ALO     | At-auction Limit Order                          |
| ODD     | Odd Lots Order                                  |
| LIT     | Limit If Touched                                |
| MIT     | Market If Touched                               |
| TSLPAMT | Trailing Limit If Touched (Trailing Amount)     |
| TSLPPCT | Trailing Limit If Touched (Trailing Percent)    |
| SLO     | Special Limit Order. Not Support Replace Order. |

- Description: US stock support order type

| Enum    | Description                                   |
| ------- | --------------------------------------------- |
| LO      | Limit Order                                   |
| MO      | Market Order                                  |
| LIT     | Limit If Touched                              |
| MIT     | Market If Touched                             |
| TSLPAMT | Trailing Limit If Touched (Trailing Amount)   |
| TSLPPCT | Trailing Limit If Touched (Trailing Percent)  |

## OrderStatus

- Description: Order Status

| Enum                 | Description                     |
| -------------------- | ------------------------------- |
| NotReported          | NotReported                     |
| ReplacedNotReported  | NotReported (Replaced Order)    |
| ProtectedNotReported | NotReported (Protected Order)   |
| VarietiesNotReported | NotReported (Conditional Order) |
| FilledStatus         | Filled                          |
| WaitToNew            | Wait To New                     |
| NewStatus            | New                             |
| WaitToReplace        | Wait To Replace                 |
| PendingReplaceStatus | Pending Replace                 |
| ReplacedStatus       | Replaced                        |
| PartialFilledStatus  | Partial Filled                  |
| WaitToCancel         | Wait To Cancel                  |
| PendingCancelStatus  | Pending Cancel                  |
| RejectedStatus       | Rejected                        |
| CanceledStatus       | Canceled                        |
| ExpiredStatus        | Expired                         |
| PartialWithdrawal    | Partial Withdrawal              |

## Market

- Description: Market

| Enum | Description                     |
| ---- | ------------------------------- |
| HK   | Hong Kong Market                |
| US   | United States of America Market |

## WebSocket Notification

- Description: Push notification field description

| field              | type   | Description                                                                                                           |
| ------------------ | ------ | --------------------------------------------------------------------------------------------------------------------- |
| side               | string | order side<br/><br/>**Enum Value**<br/>`Buy`<br />`Sell`                                                              |
| stock_name         | string | stock name                                                                                                            |
| submitted_quantity | string | submitted quantity                                                                                                    |
| symbol             | string | order symbol                                                                                                          |
| order_type         | string | [Order Type](./trade-definition#ordertype)                                                                            |
| submitted_price    | string | submitted price                                                                                                       |
| executed_quantity  | string | executed quantity                                                                                                     |
| executed_price     | string | executed price                                                                                                        |
| order_id           | string | order id                                                                                                              |
| currency           | string | currency                                                                                                              |
| status             | string | [order status](./trade-definition#orderstatus)                                                                        |
| submitted_at       | string | submitted time，formatted as a timestamp (second)                                                                     |
| updated_at         | string | last updated time ，formatted as a timestamp (second)                                                                 |
| trigger_price      | string | "`LIT` / `MIT` order trigger price"                                                                                   |
| msg                | string | rejected message or remark                                                                                            |
| tag                | string | order tag<br/><br/>**Enum Value**<br/>`Normal` - Normal Order<br />`GTC` - Long term Order<br />`Grey` - Grey Order   |
| trigger_status     | string | conditional order trigger status<br/><br/>**Enum Value**<br/>`NOT_USED`<br />`DEACTIVE`<br />`ACTIVE`<br />`RELEASED` |
| trigger_at         | string | conditional order trigger time. formatted as a timestamp (second)                                                     |
| trailing_amount    | string | "`TSLPAMT` order trailing amount"                                                                          |
| trailing_percent   | string | "`TSLPPCT` order trailing percent"                                                                         |
| limit_offset       | string | "`TSLPAMT` / `TSLPPCT` order limit offset amount"                                                                     |
| account_no         | string | account no                                                                                                            |
| remark         | string | remark message                                                                                                           |
| last_share         | string | last share |
| last_price         | string | last price |

### example

```JSON
{
	"event": "order_changed_lb",
	"data": {
		"side": "Buy",
		"stock_name": "Tencent Holdings Ltd.",
		"submitted_quantity": "1000",
		"symbol": "700.HK",
		"order_type": "LO",
		"submitted_price": "213.2",
		"executed_quantity": "1000",
		"executed_price": "213.2",
		"order_id": "27",
		"currency": "HKD",
		"status": "NewStatus",
		"submitted_at": "**********",
		"updated_at": "**********",
		"trigger_price": "213.0",
		"msg": "Insufficient Qty - 1000",
		"tag": "GTC",
		"trigger_status": "ACTIVE",
		"trigger_at": "**********",
		"trailing_amount": "5",
		"trailing_percent": "1",
		"limit_offset": "0.01",
		"account_no": "HK123445",
		"last_share": "100",
		"last_price": "234",
		"remark": "abc"
	}
}
```
