# 数据-多个时间段

> 原文： [https://www.backtrader.com/blog/posts/2015-08-24-data-multitimeframe/data-multitimeframe/](https://www.backtrader.com/blog/posts/2015-08-24-data-multitimeframe/data-multitimeframe/)

有时，投资决策采用不同的时间框架：

*   每周评估一次趋势

*   每日执行输入

或者 5 分钟对 60 分钟。

这意味着需要在 backtrader 中组合多个时间段的数据来支持这种组合。

对它的本机支持已经内置。最终用户只能遵守以下规则：

*   具有最小时间帧（因此条数较大）的数据必须是要添加到脑波实例的 1<sup>st</sup>数据

*   数据必须正确地与日期和时间对齐，平台才能从中获得任何意义

除此之外，最终用户可以在更短/更长的时间范围内自由应用所需的指标。当然：

*   适用于较大时间段的指标将产生较少的条带

该平台还将考虑以下内容

*   较大时间段的最短期限

最短时间段，可能会产生副作用，即在脑力图中添加的策略开始实施之前，必须消耗几个数量级的较小时间段。

内置的`DataResampler`将用于创建更大的时间范围。

下面是一些示例，但首先是测试脚本的要点。

```py
 # Load the Data
    datapath = args.dataname or '../datas/sample/2006-day-001.txt'
    data = btfeeds.BacktraderCSVData(
        dataname=datapath)

    tframes = dict(
        daily=bt.TimeFrame.Days,
        weekly=bt.TimeFrame.Weeks,
        monthly=bt.TimeFrame.Months)

    # Handy dictionary for the argument timeframe conversion
    # Resample the data
    if args.noresample:
        datapath = args.dataname2 or '../datas/sample/2006-week-001.txt'
        data2 = btfeeds.BacktraderCSVData(
            dataname=datapath)
    else:
        data2 = bt.DataResampler(
            dataname=data,
            timeframe=tframes[args.timeframe],
            compression=args.compression) 
```

步骤如下：

*   加载数据

*   根据用户指定的参数对其重新采样

    该脚本还允许加载 2<sup>nd</sup>数据

*   将数据添加到大脑

*   将重新采样的数据（较大的时间段）添加到大脑中

*   跑

## 示例 1-每日和每周

脚本的调用：

```py
$ ./data-multitimeframe.py --timeframe weekly --compression 1 
```

以及输出图表：

[![!image](img/cbbf6c75d90c0e12f1f6240477becc81.png)](../multitimeframe-daily-weekly.png)

## 示例 2-每日和每日压缩（2 巴至 1 巴）

脚本的调用：

```py
$ ./data-multitimeframe.py --timeframe daily --compression 2 
```

以及输出图表：

[![!image](img/096f133f671bb9ca74ee1c03d1d1731c.png)](../multitimeframe-daily-daily-2.png)

## 示例 3-具有 SMA 的策略

虽然绘图很好，但这里的关键问题是显示更大的时间范围如何影响系统，特别是当它涉及到起点时

脚本可以采用`--indicators`添加策略，在较小的时间段数据上创建**周期 10**的简单移动平均值。

如果只考虑较小的时间范围：

*   `next`将在 10 条之后首先调用，这是简单移动平均线产生值所需的时间

    笔记

    请记住，战略监控创建的指标，只有在所有指标都产生了值时才调用`next`。其基本原理是，最终用户已添加指标，以便在逻辑中使用它们，因此，如果指标未产生任何值，则不应发生逻辑

但是在这种情况下，较大的时间框架（每周）会延迟调用`next`，直到每周数据的简单移动平均值产生一个值，这需要…10 周。

脚本覆盖了只调用一次的`nextstart`，默认调用`next`以显示首次调用的时间。

### 调用 1：

只有较小的时间段（每天）得到一个简单的移动平均线

命令行和输出

```py
$ ./data-multitimeframe.py --timeframe weekly --compression 1 --indicators --onlydaily
--------------------------------------------------
nextstart called with len 10
-------------------------------------------------- 
```

还有图表。

[![!image](img/9788762f422a669cd82b44aeb072e0ac.png)](../multitimeframe-daily-weekly-indicators-onlydaily.png)

### 第 2 段：

两个时间段都有一个简单的移动平均线

命令行：

```py
$ ./data-multitimeframe.py --timeframe weekly --compression 1 --indicators
--------------------------------------------------
nextstart called with len 50
--------------------------------------------------
--------------------------------------------------
nextstart called with len 51
--------------------------------------------------
--------------------------------------------------
nextstart called with len 52
--------------------------------------------------
--------------------------------------------------
nextstart called with len 53
--------------------------------------------------
--------------------------------------------------
nextstart called with len 54
-------------------------------------------------- 
```

这里需要注意两件事：

*   策略不是在**10**时段后调用，而是在 50 时段后调用 1<sup>st</sup>。

    之所以如此，是因为在较大的（每周）时间范围内应用的简单移动平均值在 10 周后产生一个值…即 10 周*5 天/周…50 天

*   `nextstart`被叫了 5 次而不是 1 次。

    这是一个自然的副作用，混合了时间框架，并将（在本例中只有一个）指标应用于更大的时间框架。

    较大的时间段简单移动平均线产生 5 倍相同的值，同时每天消耗 5 条。

    由于周期的开始由更大的时间段控制，因此`nextstart`被调用了 5 次。

还有图表。

[![!image](img/d7e56d0e27f93d43fc62a0dfc6e0ace5.png)](../multitimeframe-daily-weekly-indicators.png)

## 结论

在`backtrader`中可以使用多个时间段数据，无需特殊对象或调整：只需先添加较小的时间段即可。

测试脚本。

```py
from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

import argparse

import backtrader as bt
import backtrader.feeds as btfeeds
import backtrader.indicators as btind

class SMAStrategy(bt.Strategy):
    params = (
        ('period', 10),
        ('onlydaily', False),
    )

    def __init__(self):
        self.sma_small_tf = btind.SMA(self.data, period=self.p.period)
        if not self.p.onlydaily:
            self.sma_large_tf = btind.SMA(self.data1, period=self.p.period)

    def nextstart(self):
        print('--------------------------------------------------')
        print('nextstart called with len', len(self))
        print('--------------------------------------------------')

        super(SMAStrategy, self).nextstart()

def runstrat():
    args = parse_args()

    # Create a cerebro entity
    cerebro = bt.Cerebro(stdstats=False)

    # Add a strategy
    if not args.indicators:
        cerebro.addstrategy(bt.Strategy)
    else:
        cerebro.addstrategy(
            SMAStrategy,

            # args for the strategy
            period=args.period,
            onlydaily=args.onlydaily,
        )

    # Load the Data
    datapath = args.dataname or '../datas/sample/2006-day-001.txt'
    data = btfeeds.BacktraderCSVData(
        dataname=datapath)

    tframes = dict(
        daily=bt.TimeFrame.Days,
        weekly=bt.TimeFrame.Weeks,
        monthly=bt.TimeFrame.Months)

    # Handy dictionary for the argument timeframe conversion
    # Resample the data
    if args.noresample:
        datapath = args.dataname2 or '../datas/sample/2006-week-001.txt'
        data2 = btfeeds.BacktraderCSVData(
            dataname=datapath)
    else:
        data2 = bt.DataResampler(
            dataname=data,
            timeframe=tframes[args.timeframe],
            compression=args.compression)

    # First add the original data - smaller timeframe
    cerebro.adddata(data)

    # And then the large timeframe
    cerebro.adddata(data2)

    # Run over everything
    cerebro.run()

    # Plot the result
    cerebro.plot(style='bar')

def parse_args():
    parser = argparse.ArgumentParser(
        description='Pandas test script')

    parser.add_argument('--dataname', default='', required=False,
                        help='File Data to Load')

    parser.add_argument('--dataname2', default='', required=False,
                        help='Larger timeframe file to load')

    parser.add_argument('--noresample', action='store_true',
                        help='Do not resample, rather load larger timeframe')

    parser.add_argument('--timeframe', default='weekly', required=False,
                        choices=['daily', 'weekly', 'monhtly'],
                        help='Timeframe to resample to')

    parser.add_argument('--compression', default=1, required=False, type=int,
                        help='Compress n bars into 1')

    parser.add_argument('--indicators', action='store_true',
                        help='Wether to apply Strategy with indicators')

    parser.add_argument('--onlydaily', action='store_true',
                        help='Indicator only to be applied to daily timeframe')

    parser.add_argument('--period', default=10, required=False, type=int,
                        help='Period to apply to indicator')

    return parser.parse_args()

if __name__ == '__main__':
    runstrat() 
```