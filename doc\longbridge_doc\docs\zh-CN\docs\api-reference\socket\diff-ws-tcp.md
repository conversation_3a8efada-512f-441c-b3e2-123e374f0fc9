---
title: WebSocket 和 TCP 接入的不同点
id: diff-ws-tcp
slug: /socket/diff_ws_tcp
sidebar_position: 6
---

LongPort 行情长连接同时支持 `WebSocket` 和 `TCP` 的接入，不同点主要如下：

- TCP 数据是流式的，客户端编写难度比 WebSocket 要大
- `WebSocket` 握手包通过 [URL Query 发送](./protocol/handshake#websocket-链接如何握手)
- `WebSocket` 的 [心跳](./control-command#心跳) 通过 `WebSocket` 协议本身的心跳 `Ping-Pong` 进行
- `WebSocket` 通信使用 `TLS` 进行加密，而 `TCP` 暂时没有

可以根据自己的需求选择，长连接协议是两者都适用的。

> 使用 `WebSocket` 接入较方便。如果对速度有更高的要求，行情可以接入 `TCP`。
