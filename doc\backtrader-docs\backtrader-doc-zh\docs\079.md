# 观察员参考

> 原文： [https://www.backtrader.com/docu/observers-reference/](https://www.backtrader.com/docu/observers-reference/)

## 基准

#### 类 backtrader.observators.Benchmark（）

该观察者存储策略的*返回*和作为传递给系统的数据之一的参考资产的*返回*。

参数：

*   `timeframe`（默认值：`None`）如果`None`，则将报告整个回溯测试期间的完整回报

*   `compression`（默认为`None`）

    仅用于子天时间范围，例如，通过指定“timeframe.Minutes”和 60 作为压缩，在每小时时间范围内工作

*   `data`（默认为`None`）

    要跟踪的参考资产，以便进行比较。

    **注**：此数据必须是通过`addata`、`resampledata`或`replaydata`添加到`cerebro`实例中的。

*   `_doprenext`（默认为`False`）

    基准测试将从战略开始的时间点开始（即：达到战略的最短期限时）。

    将其设置为`True`将记录数据馈送起点的基准值

*   `firstopen`（默认为`False`）

    将其保留为`False`确保价值与基准之间的 1<sup>st</sup>比较点从 0%开始，因为基准不会使用其开盘价。

    有关参数含义的完整说明，请参见`TimeReturn`分析仪参考

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

记住，在`run`的任何时刻，都可以通过查看索引`0`处的*行*名称来检查当前值。

## 经纪人

#### 类 backtrader.observators.Broker（*args，**kwargs）

该观察员跟踪经纪人的当前现金金额和投资组合价值（包括现金）

参数：没有

### 经纪人-现金

#### 类 backtrader.observators.Cash（*args，**kwargs）

该观察者跟踪经纪人当前的现金金额

参数：没有

### 经纪人价值

#### 类 backtrader.observators.Value（*args，**kwargs）

该观察者跟踪经纪人的当前投资组合价值，包括现金

参数：

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

## 买卖

#### 类 backtrader.observators.BuySell（*args，**kwargs）

该观察者跟踪单个买入/卖出订单（单个执行），并将它们沿着执行价格水平的数据绘制在图表上

参数：

```py
* `barplot` (default: `False`) Plot buy signals below the minimum and
  sell signals above the maximum.

  If `False` it will plot on the average price of executions during a
  bar

* `bardist` (default: `0.015` 1.5%) Distance to max/min when
  `barplot` is `True` 
```

## 缩编

#### 类 backtrader.Observators.DrawDown（）

此观察者跟踪当前下降级别（已打印）和最大下降级别（未打印）

参数：

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

## 报时表

#### 类 backtrader.observators.TimeReturn（）

该观察者存储策略的*返回*。

参数：

*   `timeframe`（默认值：`None`）如果`None`，则将报告整个回溯测试期间的完整回报

    通过 PoT T0 来考虑没有时间约束的整个数据集

*   `compression`（默认为`None`）

    仅用于子天时间范围，例如，通过指定“timeframe.Minutes”和 60 作为压缩，在每小时时间范围内工作

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

记住，在`run`的任何时刻，都可以通过查看索引`0`处的*行*名称来检查当前值。

## 交易

#### 类 backtrader.observators.Trades（）

该观察员跟踪完整交易，并绘制交易结束时达到的 PnL 水平。

当仓位从 0（或越过 0）到 X 时，交易是开放的，然后当仓位回到 0（或在相反方向越过 0）时，交易是封闭的

参数：

```py
* `pnlcomm` (def: `True`)

  Show net/profit and loss, i.e.: after commission. If set to `False`
  if will show the result of trades before commission 
```

## 日志返回

#### 类 backtrader.observer.LogReturns（）

该观察者存储策略或策略的*日志返回*

参数：

*   `timeframe`（默认值：`None`）如果`None`，则将报告整个回溯测试期间的完整回报

    通过 PoT T0 来考虑没有时间约束的整个数据集

*   `compression`（默认为`None`）

    仅用于子天时间范围，例如，通过指定“timeframe.Minutes”和 60 作为压缩，在每小时时间范围内工作

*   `fund`（默认为`None`）

    如果`None`将自动检测经纪人的实际模式（fundmode-真/假），以确定回报是基于总资产净值还是基于基金价值。参见经纪人文档中的`set_fundmode`

    对于特定行为，将其设置为`True`或`False`

记住，在`run`的任何时刻，都可以通过查看索引`0`处的*行*名称来检查当前值。

## 日志返回 2

#### 类 backtrader.observer.LogReturns2（）

扩展观察者日志返回以显示两个仪器

## 基金价值

#### 类 backtrader.Observators.FundValue（*args，**kwargs）

该观察者跟踪当前类似基金的价值

参数：没有

## 基金份额

#### 类 backtrader.Observators.FundShares（*args，**kwargs）

该观察员跟踪当前的基金类股票

参数：没有