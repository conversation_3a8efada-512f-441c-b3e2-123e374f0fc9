# Kalman 等人。

> 原文： [https://www.backtrader.com/blog/posts/2017-02-14-kalman-et-al/kalman-et-al/](https://www.backtrader.com/blog/posts/2017-02-14-kalman-et-al/kalman-et-al/)

笔记

对以下指令的支持从 commit 开始

# 开发分支中的 1146c83d9f9832630e97daab3ec7359705dc2c77

发布`1.9.30.x`将是包含它的 1<sup>st</sup>。

*backtrader*的最初目标之一是成为纯 python，即：只使用标准发行版中可用的包。`matplotlib`有一个例外，即在不重新发明轮子的情况下进行绘图。虽然在最新可能的时刻导入，以避免中断可能根本不需要打印的标准操作（并避免未安装或不希望出现错误）

随着实时数据馈送（可能位于本地时区之外）的出现，在增加对时区的支持时，对`pytz`进行了部分的第 2<sup>和</sup>例外。再次在后台执行*导入*操作，并且只有`pytz`可用（用户可以选择传递`pytz`实例）

但现在是一个完全例外的时刻，因为*反向交易者*使用的是`numpy`、`pandas`、`statsmodel`等知名软件包，以及`pykalman`等更为温和的软件包。或者在平台中包含使用这些包的东西。

社区的一些例子：

*   [移植数据帧相关指示符](https://community.backtrader.com/topic/11/porting-a-pandas-dataframe-dependent-indicator)

*   [线性回归与 std#211](https://community.backtrader.com/topic/5/linear-regression-and-std-211)

*   [机器学习+反向交易者](https://community.backtrader.com/topic/102/machine-learning-backtrader)

此愿望已添加到此处绘制的快速路线图中：

*   [v1.x-快速路线图](https://community.backtrader.com/topic/195/v1-x-quick-roadmap)

## 声明式方法

保持*backtrader*的原始精神，同时允许使用这些软件包的关键是不要**强迫**纯 python 用户必须安装这些软件包。

尽管这看起来很有挑战性，并且容易出现多个条件语句以及异常处理，但平台内部和用户外部的方法是**依赖于已经用于开发其他概念**的相同原则，例如**参数**（命名为*参数*）用于大多数对象。

让我们回忆一下如何定义`Indicator`接受*参数*和定义*行*：

```py
class MyIndicator(bt.Indicator):
    lines = ('myline',)
    params = (
        ('period', 50),
    ) 
```

参数`period`稍后可寻址为`self.params.period`或`self.p.period`，如中所示：

```py
def __init__(self):
    print('my period is:', self.p.period) 
```

行中的当前值为`self.lines.myline`或`self.l.myline`如中所示：

```py
def next(self):
    print('mylines[0]:', self.lines.myline[0]) 
```

这并不是特别有用，只是展示了**参数**背景机制的**声明性**方法，它还具有对继承的适当支持（包括*多重继承*）

## 介绍`packages`

使用相同的声明性技术（有些人称之为*元编程*），对外部*包*的支持如下：

```py
class MyIndicator(bt.Indicator):
    packages = ('pandas',)
    lines = ('myline',)
    params = (
        ('period', 50),
    ) 
```

起泡的藤壶！！！这似乎只是另一个宣言。指标实施者的第一个问题是：

*   *我必须手动导入“熊猫”吗？*

答案很简单：**不**。后台机器将导入`pandas`并使其在定义`MyIndicator`的模块中可用。现在可以在`next`中执行以下操作：

```py
def next(self):
    print('mylines[0]:', pandas.SomeFunction(self.lines.myline[0])) 
```

`packages`指令也可用于：

*   在一个声明中导入多个包

*   将导入分配给别名 ala`import pandas as pd`

假设 statsmodel 也希望`sm`完成`pandas.SomeFunction`：

```py
class MyIndicator(bt.Indicator):
    packages = ('pandas', ('statsmodel', 'sm'),)
    lines = ('myline',)
    params = (
        ('period', 50),
    )

    def next(self):
        print('mylines[0]:', sm.XX(pandas.SomeFunction(self.lines.myline[0]))) 
```

`statsmodel`已作为`sm`导入并可用。只需传递一个 iterable（一个`tuple`是*backtrader*约定）以及包的名称和所需的别名。

## 加入`frompackages`

Python 以不断查找事物而闻名，这也是该语言在动态性、内省功能和元编程方面非常出色的原因之一。同时也是无法提供相同性能的原因之一。

通常的加速方法之一是通过直接从模块中导入符号来删除模块中的查找，以便进行本地查找。我们从`pandas`开始的`SomeFunction`看起来像：

```py
from pandas import SomeFunction 
```

或使用别名：

```py
from pandas import SomeFunction as SomeFunc 
```

*反向交易者*通过`frompackages`指令为两者提供支持。让我们重做`MyIndicator`：

```py
class MyIndicator(bt.Indicator):
    frompackages = (('pandas', 'SomeFunction'),)
    lines = ('myline',)
    params = (
        ('period', 50),
    )

    def next(self):
        print('mylines[0]:', SomeFunction(self.lines.myline[0])) 
```

当然，这将开始添加更多的括号。例如，如果将从`pandas`导入两（2）件物品，则看起来是这样的：

```py
class MyIndicator(bt.Indicator):
    frompackages = (('pandas', ['SomeFunction', 'SomeFunction2']),)
    lines = ('myline',)
    params = (
        ('period', 50),
    )

    def next(self):
        print('mylines[0]:', SomeFunction2(SomeFunction(self.lines.myline[0]))) 
```

为了清楚起见，将`SomeFunction`和`SomeFunction2`放在`list`而不是`tuple`中，以便使用方括号`[]`并能够更好地阅读。

也可以将`SomeFunction`别名为`SFunc`等。完整示例：

```py
class MyIndicator(bt.Indicator):
    frompackages = (('pandas', [('SomeFunction', 'SFunc'), 'SomeFunction2']),)
    lines = ('myline',)
    params = (
        ('period', 50),
    )

    def next(self):
        print('mylines[0]:', SomeFunction2(SFunc(self.lines.myline[0]))) 
```

从不同的包中导入是可能的，但需要更多的括号。当然，换行符和缩进有助于：

```py
class MyIndicator(bt.Indicator):
    frompackages = (
        ('pandas', [('SomeFunction', 'SFunc'), 'SomeFunction2']),
        ('statsmodel', 'XX'),
    )
    lines = ('myline',)
    params = (
        ('period', 50),
    )

    def next(self):
        print('mylines[0]:', XX(SomeFunction2(SFunc(self.lines.myline[0])))) 
```

## 使用继承

`packages`和`frompackages`都支持（多重）继承。例如，可能有一个基类向所有子类添加了`numpy`支持：

```py
class NumPySupport(object):
    packages = ('numpy',)

class MyIndicator(bt.Indicator, NumPySupport):
    packages = ('pandas',) 
```

`MyIndicator`需要从后台机器进口`numpy`和`pandas`并能够使用它们。

## 介绍 Kalman 和朋友

笔记

以下两个指标都需要同行审查以确认实施情况。小心使用。

下面可以找到实现`KalmanMovingAverage`的示例。这是模仿这里的一篇帖子：[量子论系列讲座：卡尔曼滤波器](https://www.quantopian.com/posts/quantopian-lecture-series-kalman-filters)

实施：

```py
class KalmanMovingAverage(bt.indicators.MovingAverageBase):
    packages = ('pykalman',)
    frompackages = (('pykalman', [('KalmanFilter', 'KF')]),)
    lines = ('kma',)
    alias = ('KMA',)
    params = (
        ('initial_state_covariance', 1.0),
        ('observation_covariance', 1.0),
        ('transition_covariance', 0.05),
    )

    plotlines = dict(cov=dict(_plotskip=True))

    def __init__(self):
        self.addminperiod(self.p.period)  # when to deliver values
        self._dlast = self.data(-1)  # get previous day value

    def nextstart(self):
        self._k1 = self._dlast[0]
        self._c1 = self.p.initial_state_covariance

        self._kf = pykalman.KalmanFilter(
            transition_matrices=[1],
            observation_matrices=[1],
            observation_covariance=self.p.observation_covariance,
            transition_covariance=self.p.transition_covariance,
            initial_state_mean=self._k1,
            initial_state_covariance=self._c1,
        )

        self.next()

    def next(self):
        k1, self._c1 = self._kf.filter_update(self._k1, self._c1, self.data[0])
        self.lines.kma[0] = self._k1 = k1 
```

在这里的帖子后面有一个`KalmanFilter`：[基于卡尔曼滤波的 QSTrader](https://www.quantstart.com/articles/kalman-filter-based-pairs-trading-strategy-in-qstrader)成对交易策略

```py
class NumPy(object):
    packages = (('numpy', 'np'),)

class KalmanFilterInd(bt.Indicator, NumPy):
    _mindatas = 2  # needs at least 2 data feeds

    packages = ('pandas',)
    lines = ('et', 'sqrt_qt')

    params = dict(
        delta=1e-4,
        vt=1e-3,
    )

    def __init__(self):
        self.wt = self.p.delta / (1 - self.p.delta) * np.eye(2)
        self.theta = np.zeros(2)
        self.P = np.zeros((2, 2))
        self.R = None

        self.d1_prev = self.data1(-1)  # data1 yesterday's price

    def next(self):
        F = np.asarray([self.data0[0], 1.0]).reshape((1, 2))
        y = self.d1_prev[0]

        if self.R is not None:  # self.R starts as None, self.C set below
            self.R = self.C + self.wt
        else:
            self.R = np.zeros((2, 2))

        yhat = F.dot(self.theta)
        et = y - yhat

        # Q_t is the variance of the prediction of observations and hence
        # \sqrt{Q_t} is the standard deviation of the predictions
        Qt = F.dot(self.R).dot(F.T) + self.p.vt
        sqrt_Qt = np.sqrt(Qt)

        # The posterior value of the states \theta_t is distributed as a
        # multivariate Gaussian with mean m_t and variance-covariance C_t
        At = self.R.dot(F.T) / Qt
        self.theta = self.theta + At.flatten() * et
        self.C = self.R - At * F.dot(self.R)

        # Fill the lines
        self.lines.et[0] = et
        self.lines.sqrt_qt[0] = sqrt_Qt 
```

为了说明`packages`如何与继承一起工作（实际上并不需要`pandas`）

示例的执行：

```py
$ ./kalman-things.py --plot 
```

生成此图表

[![!image](img/10ae3c6245499d5f8732240a2bd6e431.png)](../kalman-et-al.png)

## 样本使用

```py
$ ./kalman-things.py --help
usage: kalman-things.py [-h] [--data0 DATA0] [--data1 DATA1]
                        [--fromdate FROMDATE] [--todate TODATE]
                        [--cerebro kwargs] [--broker kwargs] [--sizer kwargs]
                        [--strat kwargs] [--plot [kwargs]]

Packages and Kalman

optional arguments:
  -h, --help           show this help message and exit
  --data0 DATA0        Data to read in (default:
                       ../../datas/nvda-1999-2014.txt)
  --data1 DATA1        Data to read in (default:
                       ../../datas/orcl-1995-2014.txt)
  --fromdate FROMDATE  Date[time] in YYYY-MM-DD[THH:MM:SS] format (default:
                       2006-01-01)
  --todate TODATE      Date[time] in YYYY-MM-DD[THH:MM:SS] format (default:
                       2007-01-01)
  --cerebro kwargs     kwargs in key=value format (default: runonce=False)
  --broker kwargs      kwargs in key=value format (default: )
  --sizer kwargs       kwargs in key=value format (default: )
  --strat kwargs       kwargs in key=value format (default: )
  --plot [kwargs]      kwargs in key=value format (default: ) 
```

## 示例代码

```py
from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

import argparse
import datetime

import backtrader as bt

class KalmanMovingAverage(bt.indicators.MovingAverageBase):
    packages = ('pykalman',)
    frompackages = (('pykalman', [('KalmanFilter', 'KF')]),)
    lines = ('kma',)
    alias = ('KMA',)
    params = (
        ('initial_state_covariance', 1.0),
        ('observation_covariance', 1.0),
        ('transition_covariance', 0.05),
    )

    def __init__(self):
        self.addminperiod(self.p.period)  # when to deliver values
        self._dlast = self.data(-1)  # get previous day value

    def nextstart(self):
        self._k1 = self._dlast[0]
        self._c1 = self.p.initial_state_covariance

        self._kf = pykalman.KalmanFilter(
            transition_matrices=[1],
            observation_matrices=[1],
            observation_covariance=self.p.observation_covariance,
            transition_covariance=self.p.transition_covariance,
            initial_state_mean=self._k1,
            initial_state_covariance=self._c1,
        )

        self.next()

    def next(self):
        k1, self._c1 = self._kf.filter_update(self._k1, self._c1, self.data[0])
        self.lines.kma[0] = self._k1 = k1

class NumPy(object):
    packages = (('numpy', 'np'),)

class KalmanFilterInd(bt.Indicator, NumPy):
    _mindatas = 2  # needs at least 2 data feeds

    packages = ('pandas',)
    lines = ('et', 'sqrt_qt')

    params = dict(
        delta=1e-4,
        vt=1e-3,
    )

    def __init__(self):
        self.wt = self.p.delta / (1 - self.p.delta) * np.eye(2)
        self.theta = np.zeros(2)
        self.R = None

        self.d1_prev = self.data1(-1)  # data1 yesterday's price

    def next(self):
        F = np.asarray([self.data0[0], 1.0]).reshape((1, 2))
        y = self.d1_prev[0]

        if self.R is not None:  # self.R starts as None, self.C set below
            self.R = self.C + self.wt
        else:
            self.R = np.zeros((2, 2))

        yhat = F.dot(self.theta)
        et = y - yhat

        # Q_t is the variance of the prediction of observations and hence
        # \sqrt{Q_t} is the standard deviation of the predictions
        Qt = F.dot(self.R).dot(F.T) + self.p.vt
        sqrt_Qt = np.sqrt(Qt)

        # The posterior value of the states \theta_t is distributed as a
        # multivariate Gaussian with mean m_t and variance-covariance C_t
        At = self.R.dot(F.T) / Qt
        self.theta = self.theta + At.flatten() * et
        self.C = self.R - At * F.dot(self.R)

        # Fill the lines
        self.lines.et[0] = et
        self.lines.sqrt_qt[0] = sqrt_Qt

class KalmanSignals(bt.Indicator):
    _mindatas = 2  # needs at least 2 data feeds

    lines = ('long', 'short',)

    def __init__(self):
        kf = KalmanFilterInd()
        et, sqrt_qt = kf.lines.et, kf.lines.sqrt_qt

        self.lines.long = et < -1.0 * sqrt_qt
        # longexit is et > -1.0 * sqrt_qt ... the opposite of long
        self.lines.short = et > sqrt_qt
        # shortexit is et < sqrt_qt ... the opposite of short

class St(bt.Strategy):
    params = dict(
        ksigs=False,  # attempt trading
        period=30,
    )

    def __init__(self):
        if self.p.ksigs:
            self.ksig = KalmanSignals()
            KalmanFilter()

        KalmanMovingAverage(period=self.p.period)
        bt.ind.SMA(period=self.p.period)
        if True:
            kf = KalmanFilterInd()
            kf.plotlines.sqrt_qt._plotskip = True

    def next(self):
        if not self.p.ksigs:
            return

        size = self.position.size
        if not size:
            if self.ksig.long:
                self.buy()
            elif self.ksig.short:
                self.sell()

        elif size > 0:
            if not self.ksig.long:
                self.close()
        elif not self.ksig.short:  # implicit size < 0
            self.close()

def runstrat(args=None):
    args = parse_args(args)

    cerebro = bt.Cerebro()

    # Data feed kwargs
    kwargs = dict()

    # Parse from/to-date
    dtfmt, tmfmt = '%Y-%m-%d', 'T%H:%M:%S'
    for a, d in ((getattr(args, x), x) for x in ['fromdate', 'todate']):
        if a:
            strpfmt = dtfmt + tmfmt * ('T' in a)
            kwargs[d] = datetime.datetime.strptime(a, strpfmt)

    # Data feed
    data0 = bt.feeds.YahooFinanceCSVData(dataname=args.data0, **kwargs)
    cerebro.adddata(data0)

    data1 = bt.feeds.YahooFinanceCSVData(dataname=args.data1, **kwargs)
    data1.plotmaster = data0
    cerebro.adddata(data1)

    # Broker
    cerebro.broker = bt.brokers.BackBroker(**eval('dict(' + args.broker + ')'))

    # Sizer
    cerebro.addsizer(bt.sizers.FixedSize, **eval('dict(' + args.sizer + ')'))

    # Strategy
    cerebro.addstrategy(St, **eval('dict(' + args.strat + ')'))

    # Execute
    cerebro.run(**eval('dict(' + args.cerebro + ')'))

    if args.plot:  # Plot if requested to
        cerebro.plot(**eval('dict(' + args.plot + ')'))

def parse_args(pargs=None):
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description=(
            'Packages and Kalman'
        )
    )

    parser.add_argument('--data0', default='../../datas/nvda-1999-2014.txt',
                        required=False, help='Data to read in')

    parser.add_argument('--data1', default='../../datas/orcl-1995-2014.txt',
                        required=False, help='Data to read in')

    # Defaults for dates
    parser.add_argument('--fromdate', required=False, default='2006-01-01',
                        help='Date[time] in YYYY-MM-DD[THH:MM:SS] format')

    parser.add_argument('--todate', required=False, default='2007-01-01',
                        help='Date[time] in YYYY-MM-DD[THH:MM:SS] format')

    parser.add_argument('--cerebro', required=False, default='runonce=False',
                        metavar='kwargs', help='kwargs in key=value format')

    parser.add_argument('--broker', required=False, default='',
                        metavar='kwargs', help='kwargs in key=value format')

    parser.add_argument('--sizer', required=False, default='',
                        metavar='kwargs', help='kwargs in key=value format')

    parser.add_argument('--strat', required=False, default='',
                        metavar='kwargs', help='kwargs in key=value format')

    parser.add_argument('--plot', required=False, default='',
                        nargs='?', const='{}',
                        metavar='kwargs', help='kwargs in key=value format')

    return parser.parse_args(pargs)

if __name__ == '__main__':
    runstrat() 
```