---
title: Get Socket OTP (One time password)
id: socket-otp-api
slug: /socket-token-api
sidebar_position: 2
---

# Get OTP (One Time Password) API

Our socket `Token` is one time password, you can use the `Token` to connect to quote or trade gateway. It will be expired after authing.

> Last Update at 2022-04-28

## API

| Info        |                  |
| ----------- | ---------------- |
| HTTP Method | GET              |
| HTTP URL    | /v1/socket/token |

### Request Headers

| Field         | Type   | Required | description                                           |
| ------------- | ------ | -------- | ----------------------------------------------------- |
| Authorization | string | Yes      |                                                       |
| Content-Type  | string | Yes      | **Fixed Contents**："application/json; charset=utf-8" |

### Request Parameters

## Response

### Response Body

| Field   | Type   | Description                       |
| ------- | ------ | --------------------------------- |
| code    | int    | error code, failed if not equal 0 |
| msg     | string | error description                 |
| data    | object |                                   |
| ∟otp    | string | token                             |
| ∟limit  | int    | Total connection limit            |
| ∟online | int    | Current online connection count   |

### Response Example

```json
{
  "code": 0,
  "message": "",
  "data": {
    "otp": "xxxxxxxx",
    "online": 1,
    "limit": 10
  }
}
```
