# 奥达

> 原文： [https://www.backtrader.com/docu/live/oanda/oanda/](https://www.backtrader.com/docu/live/oanda/oanda/)

与 Oanda 的集成支持以下两个方面：

*   *现场数据*投料

*   *现场交易*

## 要求

*   `oandapy`

    安装时使用：`pip install git+https://github.com/oanda/oandapy.git`

*   `pytz`（可选，不推荐）

    考虑到外汇的全球和全天候性质，选择是在`UTC`时间内工作。如果愿意，您仍然可以使用所需的输出时区。

## 示例代码

来源包含以下内容下的完整样本：

*   `samples/oandatest/oandatest.py`

## 奥达-商店

存储是实时数据馈送/交易支持的基石，在*Oanda*API 与数据馈送和代理需求之间提供了一层自适应。

*   使用以下方法提供获取*代理*实例的访问：

    *   `OandaStore.getbroker(*args, **kwargs)`
*   提供对 getter*数据*提要实例的访问

    *   `OandaStore.getedata(\*args, **kwargs)`

    在这种情况下，`**kwargs`中的许多内容与`dataname`、`fromdate`、`todate`、`sessionstart`、`sessionend`、`timeframe`、`compression`等数据源相同

    该数据可提供其他参数。检查下面的参考资料。

### 强制性参数

为了成功连接到*Oanda*，以下参数是必需的：

*   `token`（默认值：`None`：API 访问令牌

*   `account`（默认值：`None`：账号 id

这由*Oanda*提供

无论是连接到*练习*服务器还是连接到真实服务器，请使用：

*   `practice`（默认值：`False`：使用测试环境

必须定期检查账户以获取*现金*和*价值*。可通过以下方式控制周期：

*   `account_tmout`（默认值：`10.0`：账户价值刷新周期/现金刷新

## 奥恩达饲料

实例化数据：

*   根据 Oanda 指南传递符号

    *   *欧元/美元兑*必须按照 Oanda 的指南规定为`EUR_USD`。将其实例化为：

    ```py
    data = oandastore.getdata(dataname='EUR_USD', ...) 
    ```

### 时间管理

除非`tz`参数（与*pytz 兼容的*对象）被传递到数据馈送，否则所有时间输出均为上述`UTC`格式。

#### 回填

*反向交易者*未向*Oanda*提出特殊要求。对于小时间段，*Oanda*在*实践*服务器上返回的回填长度为`500`条

## OandaBroker-实时交易

### 使用代理

要使用*OandaBroker*，必须替换*大脑*创建的标准代理模拟实例。

使用*商店*型号（首选）：

```py
import backtrader as bt

cerebro = bt.Cerebro()
oandastore = bt.stores.OandaStore()
cerebro.broker = oandastore.getbroker()  # or cerebro.setbroker(...) 
```

### 经纪人-初始头寸

代理支持单个参数：

*   `use_positions`（默认值：`True`：连接到代理提供商时，使用现有头寸启动代理。

    在实例化期间设置为`False`以忽略任何现有位置

#### 异议

标准使用方面没有变化。只需使用策略中可用的方法（完整解释请参见`Strategy`参考）

*   `buy`

*   `sell`

*   `close`

*   `cancel`

### 订单执行类型

*Oanda*支持*backtrader*所需的几乎所有订单执行类型，但*Close*除外。

因此，订单执行类型仅限于：

*   `Order.Market`

*   `Order.Limit`

*   `Order.Stop`

*   `Order.StopLimit`（使用*停止*和*上限*/*下限*价格）

*   `Order.StopTrail`

*   *括号*订单通过使用`takeprofit`和`stoploss`订单成员并创建内部模拟订单来支持。

### 订单有效期

回溯测试中可用的相同有效性概念（具有`valid`至`buy`和`sell`的有效性概念）可用且具有相同的含义。因此，对于*Oanda 订单*，将`valid`参数转换为以下值：

*   `None`翻译为*良好，直到取消*

    由于未指定有效期，因此订单必须在取消前有效

*   `datetime/date`翻译为*良好截止日期*

*   `timedelta(x)`翻译为*截止日期*（此处为`timedelta(x) != timedelta()`）

    这被解释为命令从`now`+`timedelta(x)`开始生效的信号

*   `timedelta() or 0`翻译为*会话*

    已传递一个值（而不是`None`），但该值为*空*，并被解释为对当前*天*（会话）有效的订单

### 通知

标准`Order`状态将通过`notify_order`方法通知*策略*（如果被覆盖）

*   `Submitted`-订单已发送至 TWS

*   `Accepted`-已下订单

*   `Rejected`-用于实际拒绝以及订单创建期间不知道其他状态时

*   `Partial`-部分执行已发生

*   `Completed`-订单已全部执行

*   `Canceled`（或`Cancelled`）

*   `Expired`-订单因到期而取消时

## 参考

### 奥恩达商店

#### 类 backtrader.stores.OandaStore（）

单例类包装以控制到 Oanda 的连接。

参数：

*   `token`（默认值：`None`：API 访问令牌

*   `account`（默认值：`None`：账号 id

*   `practice`（默认值：`False`：使用测试环境

*   `account_tmout`（默认值：`10.0`：账户价值刷新周期/现金刷新

### 奥恩达经纪人

#### 类 backtrader.brokers.OandaBroker（**kwargs）

Oanda 的代理实现。

此类将订单/位置从 Oanda 映射到`backtrader`的内部 API。

参数：

*   `use_positions`（默认值：`True`：连接到代理提供商时，使用现有头寸启动代理。

    在实例化期间设置为`False`以忽略任何现有位置

### 奥恩达数据

#### 类 backtrader.feeds.OandaData（**kwargs）

Oanda 数据源。

参数：

*   `qcheck`（默认为`0.5`）

    如果没有接收到数据，以秒为单位的唤醒时间，以便有机会正确地重新采样/重播数据包，并在链上传递通知

*   `historical`（默认为`False`）

    如果设置为`True`，数据馈送将在第一次下载数据后停止。

    标准数据馈送参数`fromdate`和`todate`将用作参考。

    如果请求的持续时间大于 IB 允许的持续时间，则数据馈送将发出多个请求，前提是为数据选择了时间段/压缩。

*   `backfill_start`（默认为`True`）

    开始时进行回填。在单个请求中将获取最大可能的历史数据。

*   `backfill`（默认为`True`）

    断开/重新连接循环后进行回填。间隙持续时间将用于下载尽可能少的数据量

*   `backfill_from`（默认为`None`）

    可以传递一个额外的数据源来进行初始层的回填。一旦数据源耗尽，如果需要，将从 IB 进行回填。理想情况下，这意味着要从已存储的源（如磁盘上的文件）进行回填，但不限于此。

*   `bidask`（默认为`True`）

    如果`True`，则历史/回填请求将从服务器请求出价/要价

    如果为`False`，则会请求*中点*

*   `useask`（默认为`False`）

    如果使用*bidask*价格中的*ask*部分，而不是*bid*的默认使用

*   `includeFirst`（默认为`True`）

    通过将参数直接设置到 Oanda API 调用，影响历史/回填请求的 1<sup>st</sup>条的传递

*   `reconnect`（默认为`True`）

    网络连接断开时重新连接

*   `reconnections`（默认为`-1`）

    尝试重新连接的次数：`-1`表示永远

*   `reconntimeout`（默认为`5.0`）

    重新连接尝试之间等待的时间（秒）

此数据源仅支持`timeframe`和`compression`的映射，这符合 OANDA API 开发人员 Guid 中的定义：

```py
(TimeFrame.Seconds, 5): 'S5',
(TimeFrame.Seconds, 10): 'S10',
(TimeFrame.Seconds, 15): 'S15',
(TimeFrame.Seconds, 30): 'S30',
(TimeFrame.Minutes, 1): 'M1',
(TimeFrame.Minutes, 2): 'M3',
(TimeFrame.Minutes, 3): 'M3',
(TimeFrame.Minutes, 4): 'M4',
(TimeFrame.Minutes, 5): 'M5',
(TimeFrame.Minutes, 10): 'M10',
(TimeFrame.Minutes, 15): 'M15',
(TimeFrame.Minutes, 30): 'M30',
(TimeFrame.Minutes, 60): 'H1',
(TimeFrame.Minutes, 120): 'H2',
(TimeFrame.Minutes, 180): 'H3',
(TimeFrame.Minutes, 240): 'H4',
(TimeFrame.Minutes, 360): 'H6',
(TimeFrame.Minutes, 480): 'H8',
(TimeFrame.Days, 1): 'D',
(TimeFrame.Weeks, 1): 'W',
(TimeFrame.Months, 1): 'M', 
```

任何其他组合都将被拒绝