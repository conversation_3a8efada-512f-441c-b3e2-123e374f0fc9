/* .vitepress/theme/style/vp-code-title.css */

/* 整体容器样式（带阴影和圆角） */
.vp-code-block-title {
  background-color: var(--vp-code-block-bg);
  margin-bottom: 20px;
  margin-top: 16px;
  border-radius: 8px;
  box-shadow: 0 10px 30px 0 rgba(0, 0, 0, 0.4); /* 添加阴影 */
  overflow: hidden;
  position: relative; /* 确保小圆点定位 */
}

/* 标题栏样式 */
.vp-code-block-title .vp-code-block-title-bar {
  color: var(--vp-c-text-1);
  font-size: 14px;
  font-weight: bold;
}

/* 代码块标题：修正倒角、阴影、边距 */
.vp-code-block-title div[class*="language-"].vp-adaptive-theme.line-numbers-mode {
  border-radius: 8px;
  box-shadow: none;
  padding-top: 0px;
  margin-bottom: 0px;
}

/* 代码块标题：隐藏小圆点 */
.vp-code-block-title div[class*="language-"].vp-adaptive-theme.line-numbers-mode::before {
  display: none;
}

/* 代码块标题：修正行号位置 */
.vp-code-block-title .line-numbers-mode .line-numbers-wrapper {
  padding-top: 20px;
}

/* 代码块标题：修正行号右侧竖线位置 */
.vp-code-block-title .line-numbers-mode .line-numbers-wrapper::after {
  top: 20px;
  height: calc(100% - 40px);
}

/* 代码块标题（无行号）：修正倒角、阴影、边距 */
.vp-code-block-title div[class*="language-"].vp-adaptive-theme {
  border-radius: 8px;
  box-shadow: none;
  padding-top: 0px;
}

/* 代码块标题（无行号）：隐藏小圆点 */
.vp-code-block-title div[class*="language-"].vp-adaptive-theme::before {
  display: none;
}