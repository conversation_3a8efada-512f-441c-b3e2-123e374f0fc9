import { Styles } from './styles';

export const defaultStyles: Styles = {
  control: {
    root: 'control',
    wrapper: 'wrapper',
    label: 'label',
    description: 'description',
    input: 'input',
    error: 'error',
    textarea: 'text-area',
    select: 'select',
    option: 'option',
    asterisk: 'asterisk',
    required: 'required',
  },
  verticalLayout: {
    root: 'vertical-layout',
    item: 'vertical-layout-item',
  },
  horizontalLayout: {
    root: 'horizontal-layout',
    item: 'horizontal-layout-item',
  },
  group: {
    root: 'group',
    label: 'group-label',
    item: 'group-item',
  },
  arrayList: {
    root: 'array-list',
    legend: 'array-list-legend',
    addButton: 'array-list-add',
    label: 'array-list-label',
    itemWrapper: 'array-list-item-wrapper',
    noData: 'array-list-no-data',
    item: 'array-list-item',
    itemToolbar: 'array-list-item-toolbar',
    itemLabel: 'array-list-item-label',
    itemContent: 'array-list-item-content',
    itemExpanded: 'expanded',
    itemMoveUp: 'array-list-item-move-up',
    itemMoveDown: 'array-list-item-move-down',
    itemDelete: 'array-list-item-delete',
  },
  label: {
    root: 'label-element',
  },
  dialog: {
    root: 'dialog-root',
    title: 'dialog-title',
    body: 'dialog-body',
    actions: 'dialog-actions',
    buttonPrimary: 'dialog-button-primary',
    buttonSecondary: 'dialog-button-secondary',
  },
  oneOf: {
    root: 'one-of',
  },
  categorization: {
    root: 'categorization',
    category: 'categorization-category',
    selected: 'categorization-selected',
    panel: 'categorization-panel',
    stepper: 'categorization-stepper',
    stepperBadge: 'categorization-stepper-badge',
    stepperLine: 'categorization-stepper-line',
    stepperFooter: 'categorization-stepper-footer',
    stepperButtonBack: 'categorization-stepper-button-back',
    stepperButtonNext: 'categorization-stepper-button-next',
  },
};
