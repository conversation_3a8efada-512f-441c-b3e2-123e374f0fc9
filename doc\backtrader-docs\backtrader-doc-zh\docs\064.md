# 位置

> 原文： [https://www.backtrader.com/docu/position/](https://www.backtrader.com/docu/position/)

资产头寸通常从战略中检查，包括：

*   `position`（物业）或`getposition(data=None, broker=None)`

    返回该策略在 Cerbero 提供的默认`broker`中的`datas[0]`位置

位置仅表示以下各项：

*   `size`持有的资产

*   平均价格为`price`

它作为一种状态，例如可用于决定是否必须发出订单（例如：只有在没有空头头寸时才输入多头头寸）

## 参考：位置

#### 类 backtrader.position.position（大小=0，价格=0.0）

保存并更新职位的大小和价格。该对象与任何资产都没有关系。它只保留大小和价格。

成员属性：

```py
* size (int): current size of the position

* price (float): current price of the position 
```

可以使用 len（Position）测试 Position 实例，以查看 size 是否为 null