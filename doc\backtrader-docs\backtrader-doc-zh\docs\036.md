# 过滤器引用

> 原文： [https://www.backtrader.com/docu/filters-reference/](https://www.backtrader.com/docu/filters-reference/)

## 会话过滤器

#### 类 backtrader.filters.SessionFilter（数据）

此类可作为筛选器应用于数据源，并将筛选出不在常规会话时间内的日内条形图（即：交易前/交易后数据）

这是一个“非简单”过滤器，必须管理数据堆栈（在初始化和**调用**期间传递）

它不需要“最后”方法，因为它没有什么可交付的

## SessionFilterSimple

#### 类 backtrader.filters.SessionFilterSimple（数据）

此类可作为筛选器应用于数据源，并将筛选出不在常规会话时间内的日内条形图（即：交易前/交易后数据）

这是一个“简单”过滤器，不能管理数据堆栈（在初始化和**调用**期间传递）

它不需要“最后”方法，因为它没有什么可交付的

条管理将由 SimpleFilterWrapper 类完成，该类是在 DataBase.addfilter\u 简单调用中添加的

## SessionFiller

#### 类 backtrader.filters.SessionFiller（数据）

声明的会话开始/结束时间内的数据源的条形填充符。

使用声明的数据源`timeframe`和`compression`构建填充条（用于计算中间缺失时间）

参数：

*   填充价格（定义：无）：

    如果没有通过，将使用上一条的收盘价。要以一个条形图结束，例如需要时间，但它不显示在绘图中…请使用 float（'Nan'）

*   填充体积（定义：浮动（'NaN'））：

    用于填充缺少的卷的值

*   填充油（定义：浮动（'NaN'））：

    用于填充缺少的未平仓权益的值

*   跳过首次填充（定义：真）：

    在看到 1<sup>st</sup>有效栏时，不要从会话启动到该栏填充

## 日历日

#### 类 backtrader.filters.CalendarDays（数据）

将缺少的日历日添加到交易日的栏填充器

参数：

*   填充价格（定义：无）：

    > 0：填充 0 或无的给定值：使用最后一个已知收盘价-1：使用最后一条的中点（高低平均值）

*   填充体积（定义：浮动（'NaN'））：

    用于填充缺少的卷的值

*   填充油（定义：浮动（'NaN'））：

    用于填充缺少的未平仓权益的值

## 巴雷奥公开赛

#### 类 backtrader.filters.BarReplayer_Open（数据）

此过滤器将条拆分为两部分：

*   `Open`：酒吧的开盘价将用于交付四个组成部分（OHLC）相等的初始价格酒吧

    此初始条的卷/openinterest 字段为 0

*   `OHLC`：原版酒吧与原版`volume`/`openinterest`一起交付

拆分模拟重播，无需使用*重播*过滤器。

## 分日器关闭

#### 类 backtrader.filters.dayslitter\u Close（数据）

将每日条形图拆分为两部分，模拟用于重播数据的 2 个刻度：

*   第一勾：`OHLX`

    `Close`将被`Open`、`High`和`Low`的*平均值*所取代

    会话打开时间用于此勾选

和

*   第二个勾号：`CCCC`

    `Close`价格将用于价格的四个组成部分

    会话结束时间用于此勾选

将使用以下参数在 2 个刻度之间分割卷：

*   `closevol`（默认值：`0.5`）该值指示必须分配给*收盘*勾号的百分比（绝对值从 0.0 到 1.0）。其余部分将分配给`OHLX`勾号。

**本过滤器拟与**`cerebro.replaydata`一起使用

## 平津

#### 类 backtrader.filters.HeikinAshi（数据）

该过滤器重塑了开放、高、低、近三种颜色的烛台，使其成为 HeikinAshi 烛台

见：

```py
* [https://en.wikipedia.org/wiki/Candlestick_chart#Heikin_Ashi_candlesticks](https://en.wikipedia.org/wiki/Candlestick_chart#Heikin_Ashi_candlesticks)

* [http://stockcharts.com/school/doku.php?id=chart_school:chart_analysis:heikin_ashi](http://stockcharts.com/school/doku.php?id=chart_school:chart_analysis:heikin_ashi) 
```

## 伦科

#### 类 backtrader.filters.Renko（数据）

修改数据流以绘制伦科条（或砖）

参数：

*   `hilo`（默认值：*False*）使用“高”和“低”而不是“关闭”来决定是否需要新的砖块

*   默认情况下：每一块砖要考虑的尺寸。

*   `autosize`（默认值：*20.0*）如果*尺寸*为*无*，则自动计算砖块尺寸（只需将当前价格除以给定值）

*   `dynamic`（默认值：*假*）如果*真*并使用*自动调整*时，移动到新砖块时会重新计算砖块的大小。这当然会消除 Renko 砖的完美对齐。

*   `align`（默认值：*1.0*因子用于对齐砖块的价格边界。如果价格是例如*3563.25*和*对齐*是*10.0*，则得到的对齐价格将是*3560*。计算：

    *   3563.25 / 10.0 = 356.325

    *   四舍五入并删除小数->356

    *   356 * 10.0 -> 3560

见：

```py
* [http://stockcharts.com/school/doku.php?id=chart_school:chart_analysis:renko](http://stockcharts.com/school/doku.php?id=chart_school:chart_analysis:renko) 
```