# 做空现金

> 原文： [https://www.backtrader.com/blog/posts/2016-12-06-shorting-cash/shorting-cash/](https://www.backtrader.com/blog/posts/2016-12-06-shorting-cash/shorting-cash/)

从一开始*反向交易者*就可以做空任何东西，包括*类股票*和*类期货*工具。做空时，现金减少，做空资产的价值用于总净清算价值。

从一边移除，另一边添加，可以保持事物的平衡。

人们似乎更喜欢增加现金，这可能会增加支出。

通过发布`1.9.7.105`，经纪人已将默认行为更改为增加现金和移除价值。可通过参数`shortcash`进行控制，该参数默认为`True`。更改它的方式如下：

```py
cerebro.broker.set_shortcash(False) 
```

或：

```py
cerebro.broker = bt.brokers.BackBroker(shortcash=False, **other_kwargs) 
```

## 行动中

下面的示例使用标准移动平均交叉，可用于查看差异。在没有参数和新行为的情况下运行它：

```py
$ ./shortcash.py --plot 
```

[![!image](img/e28f919fa437e7e8e35ae2f211294acf.png)](../shortcash-on.png)

它可以与禁用的行为进行比较：

```py
$ ./shortcash.py --plot --broker shortcash=False 
```

[![!image](img/5bf61e8da86b48bf663059fb9f359918.png)](../shortcash-off.png)

保持不变的事物：

*   最终结果

*   交易

*   清算净值演变

    为了查看这一点，添加了一个额外的*观察者*，以确保缩放允许查看详细的进化

什么变化：

*   当`shortcash`设置为`False`时，现金永远不会超过初始水平，因为一项操作总是要花钱的

    但通过新的违约行为，我们已经可以看到 1<sup>st</sup>短期操作（恰好是 1<sup>st</sup>向系统中添加现金，然后*多头*如何从系统中扣除现金（显然，空头是 1<sup>st</sup>关闭）

## 样本使用

```py
$ ./shortcash.py --help
usage: shortcash.py [-h] [--data DATA] [--cerebro CEREBRO] [--broker BROKER]
                    [--sizer SIZER] [--strat STRAT] [--plot [kwargs]]

shortcash testing ...

optional arguments:
  -h, --help            show this help message and exit
  --data DATA           Data to read in (default:
                        ../../datas/2005-2006-day-001.txt)
  --cerebro CEREBRO     kwargs in key=value format (default: )
  --broker BROKER       kwargs in key=value format (default: )
  --sizer SIZER         kwargs in key=value format (default: )
  --strat STRAT         kwargs in key=value format (default: )
  --plot [kwargs], -p [kwargs]
                        Plot the read data applying any kwargs passed For
                        example: --plot style="candle" (to plot candles)
                        (default: None) 
```

## 示例代码

```py
from __future__ import (absolute_import, division, print_function,
                        unicode_literals)

import argparse

import backtrader as bt

class MACrossOver(bt.SignalStrategy):
    params = (('ma', bt.ind.MovAv.SMA), ('p1', 10), ('p2', 30),)

    def __init__(self):
        ma1, ma2 = self.p.ma(period=self.p.p1), self.p.ma(period=self.p.p2)
        self.signal_add(bt.SIGNAL_LONGSHORT, bt.ind.CrossOver(ma1, ma2))

def runstrat(args=None):
    args = parse_args(args)

    cerebro = bt.Cerebro()

    # Data feed
    data0 = bt.feeds.BacktraderCSVData(dataname=args.data)
    cerebro.adddata(data0)

    # Broker
    kwargs = eval('dict(' + args.broker + ')')
    cerebro.broker = bt.brokers.BackBroker(**kwargs)

    # Sizer
    kwargs = eval('dict(' + args.sizer + ')')
    cerebro.addsizer(bt.sizers.FixedSize, **kwargs)

    # Strategy
    kwargs = eval('dict(' + args.strat + ')')
    cerebro.addstrategy(MACrossOver, **kwargs)

    # better net liquidation value view
    cerebro.addobserver(bt.observers.Value)

    # Execute
    cerebro.run(**(eval('dict(' + args.cerebro + ')')))

    if args.plot:  # Plot if requested to
        cerebro.plot(**(eval('dict(' + args.plot + ')')))

def parse_args(pargs=None):

    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter,
        description='shortcash testing ...')

    parser.add_argument('--data', default='../../datas/2005-2006-day-001.txt',
                        required=False, help='Data to read in')

    parser.add_argument('--cerebro', required=False, action='store',
                        default='', help='kwargs in key=value format')

    parser.add_argument('--broker', required=False, action='store',
                        default='', help='kwargs in key=value format')

    parser.add_argument('--sizer', required=False, action='store',
                        default='', help='kwargs in key=value format')

    parser.add_argument('--strat', required=False, action='store',
                        default='', help='kwargs in key=value format')

    parser.add_argument('--plot', '-p', nargs='?', required=False,
                        metavar='kwargs', const='{}',
                        help=('Plot the read data applying any kwargs passed\n'
                              '\n'
                              'For example:\n'
                              '\n'
                              '  --plot style="candle" (to plot candles)\n'))

    return parser.parse_args(pargs)

if __name__ == '__main__':
    runstrat() 
```