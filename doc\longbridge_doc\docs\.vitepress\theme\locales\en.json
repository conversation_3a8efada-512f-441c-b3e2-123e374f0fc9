{"tryIt.send": "Send", "vitepress_theme_components_tryit_index_2": "Try it", "theme_components_tryit_renderers_controls_stringcontrolrenderer_3": "Please enter", "parameters.title": "Parameters", "parameters.noParameters": "No parameters required for this API", "authorization.title": "Authorization", "authorization.createButton": "Create Now", "HD2WD-CgkkcJJW12yOmDM": "Profile", "JJTHzcLZRxvS2W-2IwWMn": "Logout", "login_now": "Apply Now", "side-footer.section-terms.terms-of-service": "Terms of Service", "side-footer.section-terms.privacy-policy": "Privacy Policy", "_yGjtJ-hz3SY": "Real-time Quotes", "igUuE7b16XAv4XV1cz2Uo": "Real-time quotes with low latency", "IRVNYbFKip3CIbruOx4-4": "Support all product types in HK, US, and CN markets", "Uqmclqxw6dQbP3jwc6G7e": "Advanced quotes such as Level 2 and National BBO are available", "5diFZtxSkEDAgBUnWcKb5": "Support access to historical candlestick quotes data", "4asTAcBmpkMshtbQ6MTOq": "Lightning-fast Trading", "_mlScbvyg8lDxhHsROlbK": "Support the trading of stocks, options, bonds, funds, and other products in the US&HK markets", "4iQqxIDsChn9iZmM5571k": "Supports multiple order types such as general order, conditional order, and attach order", "M8nA_Jg5s7ucI5fLT93x0": "Functions include placing orders, amending orders, canceling orders, viewing account positions and funds", "yGjtJ_hz3SY1": "AI Friendly", "igUuE7b16XAv4XV1cz2Uo1": "Support LLM standards, including LLMs Text, MCP etc., for you to easily use AI to build your idea.", "_YvIt0iSGu0EGld6cWVqO": "All Product Types Supported", "OG258Y1lxxNYjOsg6gH4K": "Open an account to receive HK market LV2 quotes and CN market LV1 quotes for free", "kjNmyMdY3qW7IM4SMfPXm": "HK Market", "JK4uYamM0gon_wJOP7Hn": "Stocks", "ETF": "ETFs", "X0ttVisvS_Xsb4GKz5tux": "Warrant&CBBC", "2JEm9E33pUNr1kKiPGi7E": "US Market", "NnXaw5QqFz0uQLw3JmkqE": "Options", "5i7gs89W1HrWZNVhWswfZ": "China Connect", "gymqj97uGGjyXSp99hh-o": "Product", "u7PCsvYbglQ4mhmnQhK3N": "Trade", "pABA78lHD--FSi39tpzwf": "Quotes", "tryIt.backToDoc": "Back to Docs", "breadcrumb.home": "Home", "authorization.autoFill": "Auto filled with simulated account authorization information"}