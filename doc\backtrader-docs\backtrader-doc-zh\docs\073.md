# 文件夹概述

> 原文： [https://www.backtrader.com/docu/analyzers/pyfolio/](https://www.backtrader.com/docu/analyzers/pyfolio/)

笔记

截至（至少）2017-07-25，`pyfolio`API 已更改，`create_full_tear_sheet`不再以`gross_lev`作为命名参数。

因此，用于集成的示例不起作用

引用自[处的`pyfolio`主页 http://quantopian.github.io/pyfolio/](http://quantopian.github.io/pyfolio/) ：

```py
pyfolio is a Python library for performance and risk analysis of financial
portfolios developed by Quantopian Inc. It works well with the Zipline open
source backtesting library 
```

现在它与*反向交易者*也能很好地协同工作。需要什么：

*   `pyfolio`显然

*   以及它的依赖关系（比如`pandas`、`seaborn`…）

    笔记

    在与版本`0.5.1`集成的过程中，需要对依赖项的最新包进行更新，例如`seaborn`从先前安装的`0.7.0-dev`更新为`0.7.1`，这显然是由于缺少方法`swarmplot`

## 用法

1.  将`PyFolio`分析仪加入`cerebro`混合物中：

    ```py
    cerebro.addanalyzer(bt.analyzers.PyFolio) 
    ```

2.  运行并检索 1<sup>st</sup>策略：

    ```py
    strats = cerebro.run()
    strat0 = strats[0] 
    ```

3.  使用您为其指定的名称或将为其指定的默认名称检索分析仪：`pyfolio`。例如：

    ```py
    pyfolio = strats.analyzers.getbyname('pyfolio') 
    ```

4.  使用分析仪方法`get_pf_items`检索`pyfolio`所需的 4 种成分：

    ```py
    returns, positions, transactions, gross_lev = pyfoliozer.get_pf_items() 
    ```

    !!! 笔记

    ```py
    The integration was done looking at test samples available with
    `pyfolio` and the same headers (or absence of) has been replicated 
    ```

5.  与`pyfolio`合作（这已经超出*反向交易者*生态系统）

一些与*反向交易者*没有直接关系的使用说明

*   `pyfolio`自动绘图在*Jupyter 笔记本*外部工作，但**在**内部工作最好

*   `pyfolio`数据表的输出似乎在*Jupyter 笔记本*之外几乎不起作用。它在*笔记本*中工作

如果希望使用`pyfolio`工作，那么结论很简单：**在 Jupyter 笔记本中工作**

## 示例代码

代码如下所示：

```py
...
cerebro.addanalyzer(bt.analyzers.PyFolio, _name='pyfolio')
...
results = cerebro.run()
strat = results[0]
pyfoliozer = strat.analyzers.getbyname('pyfolio')
returns, positions, transactions, gross_lev = pyfoliozer.get_pf_items()
...
...
# pyfolio showtime
import pyfolio as pf
pf.create_full_tear_sheet(
    returns,
    positions=positions,
    transactions=transactions,
    gross_lev=gross_lev,
    live_start_date='2005-05-01',  # This date is sample specific
    round_trips=True)

# At this point tables and chart will show up 
```

### 参考

查看`PyFolio`分析仪的分析仪参考及其内部使用的分析仪