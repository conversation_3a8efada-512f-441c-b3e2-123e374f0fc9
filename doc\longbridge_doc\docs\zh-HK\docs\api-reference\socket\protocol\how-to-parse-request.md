---
title: 解析請求包
id: how-to-parse-request
slug: /socket/protocol/request
sidebar_position: 4
---

當客戶端想要請求服務端獲取數據時必須通過請求包發送相應的請求。

:::info
當包頭中的 `type` 值爲 `1` 時，數據包爲請求包
:::

結構如下：

```
  0                   1                   2                   3
 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1 2 3 4 5 6 7 8 9 0 1
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
| type=1|v|g|re.|    cmd_code   |           request_id          |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                               |            timeout            |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                    body_len                   |               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+               +
|                       body(by body_len)                       |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                                                               |
+                        nonce(optional)                        +
|                                                               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+
|                                                               |
+                                                               +
|                                                               |
+                      signature(optional)                      +
|                                                               |
+                                                               +
|                                                               |
+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+-+

```

包體字段說明如下：

| 字段       | 長度 (bit)                 | 長度（字節）| 說明                                                                                 |
| ---------- | -------------------------- | ------------ | ------------------------------------------------------------------------------------ |
| cmd_code   | 8                          | 1            | 指令 cmd 值                                                                          |
| request_id | 32(uint32)                 | 4            | 請求 id，同一個連接的 id 需要唯一，從 1 開始，到達 4294967295 後從新開始。           |
| timeout    | 16(uint16)                 | 2            | `timeout` 單位毫秒，最大 60000 (60s）                                            |
| body_len   | 24(uint32)                 | 3            | `body` 長度，單位：字節，最大 16 MB 數據；如果 gzip 爲 1，該值爲 `body` 壓縮後的長度 |
| body       | 可變長度，由 body_len 決定 | 可變長度     | `body`，最大 16 MB                                                                   |
| nonce      | 64                         | 8            | 僅當包頭中的 `verify` 爲 1 時存在                                                    |
| signature  | 128                        | 16           | 僅當包頭中的 verify 爲 1 時存在                                                      |

:::info
獲取到數據包後，通過反序列化 `body` 得到具體的業務數據
:::
