# 特征

> 原文： [https://www.backtrader.com/home/<USER>/](https://www.backtrader.com/home/<USER>/)

### 现场交易

与互动经纪人、Oanda v1、VisualChart 以及外部 3<sup>rd</sup>方经纪人（羊驼、Oanda v2、ccxt 等）

### 基于`0`的索引

*   当前使用`0`在数组中，以解决访问数组中的值时的前瞻性偏差

*   在**最后**时刻使用`-1`、`-2`（即：负值），与 Python 的定义保持同步

*   任何正指数都意味着**未来**（在`event-only`模式下测试你的代码，它会崩溃）

### 事件和向量化

*   交易逻辑和经纪人总是以事件为基础运行

*   如果可能，对指标的计算进行矢量化（可以预加载源数据）

一切都可以在`event-only`模式下运行，无需预加载数据，就像事情是实时的一样。

### 数据源（也在现场）

*   内置对多个来源的支持：*CSV*、数据库来源、YahooFinance、交互式经纪人、Oanda v1。。。

*   可以同时运行任意数量的同步数据馈送（显然内存受限）

    警告

    当心生存者的偏见！

*   可以混合运行多个时间段

*   集成重采样和重放

### 包括电池在内的经纪人

*   订单类型：`Market`、`Limit`、`Stop`、`StopLimit`、`StopTrail`、`StopTrailLimit`、`OCO`、`Bracket`、`MarketOnClose`

*   多头卖空

*   未来类工具的持续现金调整

*   用户定义的佣金方案和信用利息

*   资金模式

*   容量填充策略

*   海关拖鞋

### 策略-交易逻辑

*   运行前自动预热周期计算

*   多个策略（针对同一个代理）可以并行运行

*   多订单生成方式（`buy/sell`、`order_target_xxx`、自动信号）

*   事件通知：传入数据、数据源提供程序、订单、交易、计时器。

### 指标

船上有超过 122 个指示灯和通常的嫌疑犯

*   许多移动平均线（`SMA`、`EMA`…）、经典移动平均线（`MACD`、`Stochastic`、`RSI`…）和其他移动平均线

*   `ta-lib`整合

### 性能分析器

几个内置性能分析仪（`TimeReturns`、`TradeAnalyzer`、`SharpeRatio`、`VWR`、`SQN`…）

### 绘图（额外）

使用单个命令自动（可自定义）打印。

笔记

要使其工作，必须安装`matplotlib`

### 浆纱机

定义并插入智能自动锁定策略

### 观察员

将被绘制并可以观察系统中的所有内容的代理（通常用于绘制统计数据）

### 米塞莱纳

*   用于随时间重复操作的计时器

*   交易日历

*   时区支持

### 纯 Python

使用最强大但易于使用的编程语言之一。不需要外部库。

*   使用 OO 可以轻松地让拼图的各个部分相互匹配。

*   尽可能重载运算符，以提供自然语言结构，例如：

    ```py
    av_diff = bt.ind.SMA(period=30) - bt.ind.SMA(period=15) 
    ```

    式中，`av_diff`将包含`30`和`15`期间的简单移动平均数之差。

*   对于语言构造，不能被覆盖，如`and`、`or`、`if`等，提供了等价的*函数*以确保没有功能缺失，如

    ```py
    av_and = bt.And(av_diff > 0, self.data.close < bt.ind.SMA(period=30)) 
    ```